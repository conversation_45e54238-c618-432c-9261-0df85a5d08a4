package db.migration;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class V1_3_0__resourceUserRoleSyncProjectUserSection extends BaseJavaMigration {

    @Override
    public void migrate(Context context) throws Exception {
//        System.out.println("开始升级用户角色资源关联数据");
//        resourceUserRoleUpgrade();
//        System.out.println("结束升级用户角色资源关联数据");
    }

    @Override
    public Integer getChecksum() {
        return 130;
    }

    private static void resourceUserRoleUpgrade() throws SQLException {
        String pghost = System.getenv("POSTGRES_HOST");
        String pgport = System.getenv("POSTGRES_PORT");
        String pgusername = System.getenv("POSTGRES_USER");
        String pgpassword = System.getenv("POSTGRES_PASSWORD");

        Connection entAdminConnection = null;
        Statement entAdminStatement = null;
        ResultSet entAdminResultSet = null;

        PreparedStatement entAdminStatement2 = null;

        try{
            try {
                Class.forName("org.postgresql.Driver");
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
            String url = "jdbc:postgresql://"+pghost+":"+pgport+"/ent_admin";
            entAdminConnection = DriverManager.getConnection(url, pgusername, pgpassword);
            entAdminConnection.setAutoCommit(false);
            // 查询标段数据
            entAdminStatement = entAdminConnection.createStatement();
            entAdminStatement.execute("select section_id,user_id from project_user_section pus where pus.is_deleted = 0 and pus.section_id != 0");
            entAdminResultSet = entAdminStatement.getResultSet();
            List<Map<String, Long>> projectUserSectionList = new ArrayList<>();
            // 处理查询结果
            while (entAdminResultSet.next()) {
                long sectionId = entAdminResultSet.getLong("section_id");
                long userId = entAdminResultSet.getLong("user_id");
                Map<String, Long> sectionUserMap = new HashMap<>();
                sectionUserMap.put("sectionId", sectionId);
                sectionUserMap.put("userId", userId);
                projectUserSectionList.add(sectionUserMap);
            }
            entAdminStatement2 = entAdminConnection.prepareStatement("INSERT INTO public.resource_user_role (id, user_id, role_ids, res_id, created_by, created_at, updated_by, updated_at, is_deleted, res_type) VALUES (?, ?, '{}', ?, 0, '2024-05-28 00:00:00', 0, '2024-05-28 00:00:00', 0, 'section');");
            Snowflake snowflake = IdUtil.createSnowflake(1L, 1L);
            for(Map<String, Long> map: projectUserSectionList) {
                entAdminStatement2.setLong(1, snowflake.nextId());
                entAdminStatement2.setLong(2, map.get("userId"));
                entAdminStatement2.setLong(3, map.get("sectionId"));
                entAdminStatement2.addBatch();
            }
            entAdminStatement2.executeBatch();
            entAdminConnection.commit();
        } finally {
            dbClose(entAdminResultSet, entAdminStatement, entAdminConnection);
            dbClose(null, entAdminStatement2, null);
        }
    }

    private static void dbClose(ResultSet resultSet, Statement statement, Connection connection) throws SQLException {
        // 关闭连接
        if(resultSet != null) {
            resultSet.close();
        }
        if(statement != null) {
            statement.close();
        }
        if(connection != null) {
            connection.close();
        }
    }
}
