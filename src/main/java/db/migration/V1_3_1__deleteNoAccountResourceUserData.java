package db.migration;

import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.*;
import java.util.*;

public class V1_3_1__deleteNoAccountResourceUserData extends BaseJavaMigration {

    @Override
    public void migrate(Context context) throws Exception {
//        System.out.println("开始删除未创建账号的用户数据");
//        deleteNoAccountResourceUserData();
//        System.out.println("结束删除未创建账号的用户数据");
    }

    @Override
    public Integer getChecksum() {
        return 131;
    }

    private static void deleteNoAccountResourceUserData() throws SQLException {
        String pghost = System.getenv("POSTGRES_HOST");
        String pgport = System.getenv("POSTGRES_PORT");
        String pgusername = System.getenv("POSTGRES_USER");
        String pgpassword = System.getenv("POSTGRES_PASSWORD");

        java.sql.Date date = new java.sql.Date(System.currentTimeMillis());

        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        Connection connection2 = null;
        Statement statement2 = null;
        PreparedStatement statement3 = null;
        PreparedStatement statement4 = null;
        PreparedStatement statement5 = null;
        ResultSet resultSet2 = null;
        try {
            try {
                Class.forName("org.postgresql.Driver");
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }

            String authUrl = "jdbc:postgresql://"+pghost+":"+pgport+"/luban_auth";
            connection = DriverManager.getConnection(authUrl, pgusername, pgpassword);
            statement = connection.createStatement();
            statement.executeQuery("select id from auth_user;");
            resultSet = statement.getResultSet();
            List<Long> existAccountIds = new ArrayList<>();

            while(resultSet.next()) {
                long id = resultSet.getLong("id");
                existAccountIds.add(id);
            }

            // 建立数据库连接
            String url = "jdbc:postgresql://"+pghost+":"+pgport+"/ent_admin";
            connection2 = DriverManager.getConnection(url, pgusername, pgpassword);
            connection2.setAutoCommit(false);
            // 创建Statement对象
            statement2 = connection2.createStatement();
            // 执行查询语句
            resultSet2 = statement2.executeQuery("SELECT id, account_id from resource_user;");

            List<Long> needDeleteUserIds = new ArrayList<>();
            // 处理查询结果
            while (resultSet2.next()) {
                long id = resultSet2.getLong("id");
                long accountId = resultSet2.getLong("account_id");
                if(existAccountIds.contains(accountId)) {
                    continue;
                }
                needDeleteUserIds.add(id);
            }

            if(!needDeleteUserIds.isEmpty()) {
                statement3 = connection2.prepareStatement("UPDATE resource_user SET is_deleted = 1 WHERE ARRAY[id] <@ ?;");
                statement3.setArray(1, connection2.createArrayOf("BIGINT", needDeleteUserIds.toArray()));
                statement3.execute();
                statement4 = connection2.prepareStatement("UPDATE resource_user_org SET is_deleted = 1 WHERE ARRAY[id] <@ ?;");
                statement4.setArray(1, connection2.createArrayOf("BIGINT", needDeleteUserIds.toArray()));
                statement4.execute();
                statement5 = connection2.prepareStatement("UPDATE resource_user_role SET is_deleted = 1 WHERE ARRAY[id] <@ ?;");
                statement5.setArray(1, connection2.createArrayOf("BIGINT", needDeleteUserIds.toArray()));
                statement5.execute();
            }

            connection2.commit();
        } finally {
            dbClose(resultSet, statement, connection);
            dbClose(resultSet2, statement2, connection2);
            dbClose(null, statement3, null);
            dbClose(null, statement4, null);
            dbClose(null, statement5, null);
        }
    }

    private static void dbClose(ResultSet resultSet, Statement statement, Connection connection) throws SQLException {
        // 关闭连接
        if(resultSet != null) {
            resultSet.close();
        }
        if(statement != null) {
            statement.close();
        }
        if(connection != null) {
            connection.close();
        }
    }
}
