UPDATE "public"."sys_menu" SET "name" = 'WBS模板', "type" = 'menu', "parent_id" = ***************1829, "order_num" = 20, "url" = '/configuration/wbsTemplate', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,1723997045709606939}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata', "permission_code" = '14' WHERE "id" = 1723997045709606939;
UPDATE "public"."sys_menu" SET "name" = '基础数据', "type" = 'dir', "parent_id" = -1, "order_num" = 20, "url" = '/basic-data-web', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/basic-data/', "sys_module_code" = 'project_basicdata', "permission_code" = '13' WHERE "id" = ***************1829;
UPDATE "public"."sys_menu" SET "name" = 'WBS实例', "type" = 'menu', "parent_id" = ***************1829, "order_num" = 21, "url" = '/configuration/wbsInstance', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,1723997045709606940}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata', "permission_code" = '15' WHERE "id" = 1723997045709606940;
UPDATE "public"."sys_menu" SET "name" = '人员信息', "type" = 'menu', "parent_id" = ***************1829, "order_num" = 22, "url" = '/configuration/personaInfo', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,***************1832}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata' WHERE "id" = ***************1832;
UPDATE "public"."sys_menu" SET "name" = '基础设置', "type" = 'dir', "parent_id" = ***************1829, "order_num" = 23, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,***************1834}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata' WHERE "id" = ***************1834;
UPDATE "public"."sys_menu" SET "name" = '作业班组', "type" = 'menu', "parent_id" = ***************1834, "order_num" = 23, "url" = '/configuration/operationTeams', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,***************1834,***************1835}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata' WHERE "id" = ***************1835;
UPDATE "public"."sys_menu" SET "name" = '工作区域', "type" = 'menu', "parent_id" = ***************1834, "order_num" = 24, "url" = '/configuration/workArea', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1829,***************1834,***************1836}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_basicdata' WHERE "id" = ***************1836;
UPDATE "public"."sys_menu" SET "name" = '质保体系', "type" = 'dir', "parent_id" = ***************1837, "order_num" = 25, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1838}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1838;
UPDATE "public"."sys_menu" SET "name" = '管理制度', "type" = 'menu', "parent_id" = ***************1838, "order_num" = 25, "url" = '/system/manageRegime/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1838,***************1839}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1839;
UPDATE "public"."sys_menu" SET "name" = '质量管理', "type" = 'dir', "parent_id" = -1, "order_num" = 25, "url" = '/sphere-quality-web', "component" = '', "status" = 1, "icon" = '质量管理', "path" = '{***************1837}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/sphere/', "sys_module_code" = 'project_quality' WHERE "id" = ***************1837;
UPDATE "public"."sys_menu" SET "name" = '组织机构', "type" = 'menu', "parent_id" = ***************1838, "order_num" = 26, "url" = '/system/orginationStructure', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1838,***************1840}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1840;
UPDATE "public"."sys_menu" SET "name" = '岗位职责', "type" = 'menu', "parent_id" = ***************1838, "order_num" = 27, "url" = '/system/duty/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1838,***************1841}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1841;
UPDATE "public"."sys_menu" SET "name" = '责任书', "type" = 'menu', "parent_id" = ***************1838, "order_num" = 28, "url" = '/system/responsibilityLetter/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1838,1714256290457690000}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = 1714256290457690000;
UPDATE "public"."sys_menu" SET "name" = '隐患排查', "type" = 'menu', "parent_id" = ***************1843, "order_num" = 29, "url" = '/inspect/hiddenDangerCheck/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1843,***************1844}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1844;
UPDATE "public"."sys_menu" SET "name" = '质量检查', "type" = 'dir', "parent_id" = ***************1837, "order_num" = 29, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1843}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1843;
UPDATE "public"."sys_menu" SET "name" = '巡检任务', "type" = 'menu', "parent_id" = ***************1843, "order_num" = 30, "url" = '/inspect/patrolTask/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1843,***************1845}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1845;
UPDATE "public"."sys_menu" SET "name" = '整改记录', "type" = 'menu', "parent_id" = ***************1843, "order_num" = 31, "url" = '/inspect/rectification/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1843,***************1846}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1846;
UPDATE "public"."sys_menu" SET "name" = '技术交底', "type" = 'menu', "parent_id" = ***************1837, "order_num" = 32, "url" = '/technicExplain/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1847}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1847;
UPDATE "public"."sys_menu" SET "name" = '质量报告', "type" = 'menu', "parent_id" = ***************1837, "order_num" = 33, "url" = '/report/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,1720716936518553644}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = 1720716936518553644;
UPDATE "public"."sys_menu" SET "name" = '基础设置', "type" = 'dir', "parent_id" = ***************1837, "order_num" = 34, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1848}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1848;
UPDATE "public"."sys_menu" SET "name" = '质量检查库', "type" = 'menu', "parent_id" = ***************1848, "order_num" = 34, "url" = '/baseSetting/checkLibrary/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1848,1719671740694642733}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = 1719671740694642733;
UPDATE "public"."sys_menu" SET "name" = '编号规则', "type" = 'menu', "parent_id" = ***************1848, "order_num" = 35, "url" = '/baseSetting/codeRule/QUALITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1848,***************1855}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_quality' WHERE "id" = ***************1855;
UPDATE "public"."sys_menu" SET "name" = '流程/表单管理', "type" = 'menu', "parent_id" = ***************1848, "order_num" = 36, "url" = '/configuration/formFlowConfig', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1837,***************1848,1721819894389522481}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/basic-data/', "sys_module_code" = 'project_quality' WHERE "id" = 1721819894389522481;
UPDATE "public"."sys_menu" SET "name" = '目标管理', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 37, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1889}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1889;
UPDATE "public"."sys_menu" SET "name" = '管理制度', "type" = 'menu', "parent_id" = ***************1857, "order_num" = 37, "url" = '/system/manageRegime/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1857,***************1858}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1858;
UPDATE "public"."sys_menu" SET "name" = '安全管理', "type" = 'dir', "parent_id" = -1, "order_num" = 37, "url" = '/sphere-safety-web', "component" = '', "status" = 1, "icon" = '安全管理', "path" = '{***************1856}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/sphere/', "sys_module_code" = 'project_security' WHERE "id" = ***************1856;
UPDATE "public"."sys_menu" SET "name" = '组织机构', "type" = 'menu', "parent_id" = ***************1857, "order_num" = 38, "url" = '/system/orginationStructure', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1857,***************1859}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1859;
UPDATE "public"."sys_menu" SET "name" = '安保体系', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 38, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1857}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1857;
UPDATE "public"."sys_menu" SET "name" = '岗位职责', "type" = 'menu', "parent_id" = ***************1857, "order_num" = 39, "url" = '/system/duty/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1857,***************1860}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1860;
UPDATE "public"."sys_menu" SET "name" = '设备管理', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 40, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1861}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1861;
UPDATE "public"."sys_menu" SET "name" = '设备信息', "type" = 'menu', "parent_id" = ***************1861, "order_num" = 40, "url" = '/equipmentManagement/deviceInfo', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1861,***************1862}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1862;
UPDATE "public"."sys_menu" SET "name" = '隐患排查', "type" = 'menu', "parent_id" = ***************1863, "order_num" = 41, "url" = '/inspect/hiddenDangerCheck/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1863,***************1864}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1864;
UPDATE "public"."sys_menu" SET "name" = '安全教育', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 41, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1868}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1868;
UPDATE "public"."sys_menu" SET "name" = '安全检查', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 42, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1863}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1863;
UPDATE "public"."sys_menu" SET "name" = '巡检任务', "type" = 'menu', "parent_id" = ***************1863, "order_num" = 42, "url" = '/inspect/patrolTask/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1863,***************1865}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1865;
UPDATE "public"."sys_menu" SET "name" = '整改记录', "type" = 'menu', "parent_id" = ***************1863, "order_num" = 43, "url" = '/inspect/rectification/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1863,***************1866}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1866;
UPDATE "public"."sys_menu" SET "name" = '安全评分', "type" = 'menu', "parent_id" = ***************1863, "order_num" = 44, "url" = '/inspect/inspect', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1863,***************1867}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1867;
UPDATE "public"."sys_menu" SET "name" = '岗前教育', "type" = 'menu', "parent_id" = ***************1868, "order_num" = 45, "url" = '/safetyActivity/preEducation', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1868,***************1869}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1869;
UPDATE "public"."sys_menu" SET "name" = '教育考试', "type" = 'menu', "parent_id" = ***************1868, "order_num" = 46, "url" = '/safetyActivity/exam', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1868,***************1870}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1870;
UPDATE "public"."sys_menu" SET "name" = '安全培训', "type" = 'menu', "parent_id" = ***************1868, "order_num" = 47, "url" = '/safetyActivity/safetyTrain', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1868,***************1871}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1871;
UPDATE "public"."sys_menu" SET "name" = '安全考核', "type" = 'menu', "parent_id" = ***************1868, "order_num" = 48, "url" = '/safetyActivity/score', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1868,***************1872}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1872;
UPDATE "public"."sys_menu" SET "name" = '安全风险', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 49, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1873}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1873;
UPDATE "public"."sys_menu" SET "name" = '辨识评估', "type" = 'menu', "parent_id" = ***************1873, "order_num" = 49, "url" = '/dangerManage/dangerAssess', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1873,***************1874}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1874;
UPDATE "public"."sys_menu" SET "name" = '分级管控', "type" = 'menu', "parent_id" = ***************1873, "order_num" = 50, "url" = '/dangerManage/dangerManage', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1873,***************1875}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1875;
UPDATE "public"."sys_menu" SET "name" = '危大工程', "type" = 'menu', "parent_id" = ***************1856, "order_num" = 51, "url" = '/riskProject', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1876}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1876;
UPDATE "public"."sys_menu" SET "name" = '应急预案', "type" = 'menu', "parent_id" = ***************1877, "order_num" = 52, "url" = '/emergencyManagement/emergencyPlan', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1877,***************1878}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1878;
UPDATE "public"."sys_menu" SET "name" = '应急演练', "type" = 'menu', "parent_id" = ***************1877, "order_num" = 53, "url" = '/contingencyManagement/emergencyDrills', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1877,***************1879}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1879;
UPDATE "public"."sys_menu" SET "name" = '应急资源', "type" = 'menu', "parent_id" = ***************1877, "order_num" = 54, "url" = '/emergencyManagement/emergencySupplies', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1877,***************1880}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1880;
UPDATE "public"."sys_menu" SET "name" = '安全活动', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 55, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1881}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1881;
UPDATE "public"."sys_menu" SET "name" = '安全交底', "type" = 'menu', "parent_id" = ***************1881, "order_num" = 55, "url" = '/technicExplain/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1881,***************1882}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1882;
UPDATE "public"."sys_menu" SET "name" = '安全会议', "type" = 'menu', "parent_id" = ***************1881, "order_num" = 56, "url" = '/safetyActivity/safetyConference', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1881,***************1883}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1883;
UPDATE "public"."sys_menu" SET "name" = '专项活动', "type" = 'menu', "parent_id" = ***************1881, "order_num" = 57, "url" = '/safetyActivity/specialTraining', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1881,***************1884}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1884;
UPDATE "public"."sys_menu" SET "name" = '临电管理', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 60, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1887}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1887;
UPDATE "public"."sys_menu" SET "name" = '配电箱', "type" = 'menu', "parent_id" = ***************1887, "order_num" = 60, "url" = '/electricManage/electricBox', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1887,***************1888}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1888;
UPDATE "public"."sys_menu" SET "name" = '目标责任书', "type" = 'menu', "parent_id" = ***************1889, "order_num" = 61, "url" = '/goalManagement/dutyConclusion/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1889,1719605864688582738}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = 1719605864688582738;
UPDATE "public"."sys_menu" SET "name" = '职业健康', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 61, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1892}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1892;
UPDATE "public"."sys_menu" SET "name" = '目标考核', "type" = 'menu', "parent_id" = ***************1889, "order_num" = 62, "url" = '/goalManagement/dutyAssessment/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1889,1719605864688582739}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = 1719605864688582739;
UPDATE "public"."sys_menu" SET "name" = '安全经费', "type" = 'menu', "parent_id" = ***************1856, "order_num" = 62, "url" = '/securityFunding', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1885}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1885;
UPDATE "public"."sys_menu" SET "name" = '应急管理', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 63, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1877}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1877;
UPDATE "public"."sys_menu" SET "name" = '防护用品', "type" = 'menu', "parent_id" = ***************1892, "order_num" = 63, "url" = '/occupationHealth/protectiveArticle/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1892,***************1893}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1893;
UPDATE "public"."sys_menu" SET "name" = '事故管理', "type" = 'menu', "parent_id" = ***************1856, "order_num" = 64, "url" = '/accidentManage', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1886}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1886;
UPDATE "public"."sys_menu" SET "name" = '健康检查', "type" = 'menu', "parent_id" = ***************1892, "order_num" = 64, "url" = '/occupationHealth/HealthCheck/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1892,***************1894}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1894;
UPDATE "public"."sys_menu" SET "name" = '安全报告', "type" = 'menu', "parent_id" = ***************1856, "order_num" = 65, "url" = '/report/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,1719259302770151525}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = 1719259302770151525;
UPDATE "public"."sys_menu" SET "name" = '安全检查库', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 66, "url" = '/baseSetting/checkLibrary/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1896}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1896;
UPDATE "public"."sys_menu" SET "name" = '基础设置', "type" = 'dir', "parent_id" = ***************1856, "order_num" = 66, "url" = NULL, "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1895;
UPDATE "public"."sys_menu" SET "name" = '行为积分库', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 67, "url" = '/baseSetting/actionPointsLib/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1897}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1897;
UPDATE "public"."sys_menu" SET "name" = '考试题库', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 68, "url" = '/baseSetting/questionsBank/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1898}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1898;
UPDATE "public"."sys_menu" SET "name" = '防护用品库', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 69, "url" = '/baseSetting/materialWarehouse/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1899}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1899;
UPDATE "public"."sys_menu" SET "name" = '安全评分', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 70, "url" = '/baseSetting/securityScore/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1900}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1900;
UPDATE "public"."sys_menu" SET "name" = '安全风险库', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 71, "url" = '/baseSetting/safetyRisk/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1901}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1901;
UPDATE "public"."sys_menu" SET "name" = '编号规则', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 72, "url" = '/baseSetting/codeRule/SECURITY', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,***************1902}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_security' WHERE "id" = ***************1902;
UPDATE "public"."sys_menu" SET "name" = '流程/表单管理', "type" = 'menu', "parent_id" = ***************1895, "order_num" = 73, "url" = '/configuration/formFlowConfig', "component" = '', "status" = 1, "icon" = NULL, "path" = '{***************1856,***************1895,1715291027413221473}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/basic-data/', "sys_module_code" = 'project_security' WHERE "id" = 1715291027413221473;
UPDATE "public"."sys_menu" SET "name" = 'BIM模型', "type" = 'menu', "parent_id" = 1714256290461786118, "order_num" = 74, "url" = '/main/modelManager/bim', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786117,1714256290461786118,1714256290461786119}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786119;
UPDATE "public"."sys_menu" SET "name" = 'BIM管理', "type" = 'dir', "parent_id" = -1, "order_num" = 74, "url" = '/bim-web', "component" = '', "status" = 1, "icon" = 'BIM管理', "path" = '{1714256290461786117}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/bim/', "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786117;
UPDATE "public"."sys_menu" SET "name" = '模型管理', "type" = 'dir', "parent_id" = 1714256290461786117, "order_num" = 74, "url" = '/#', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786117,1714256290461786118}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786118;
UPDATE "public"."sys_menu" SET "name" = 'DWG模型', "type" = 'menu', "parent_id" = 1714256290461786118, "order_num" = 75, "url" = '/main/modelManager/dwg', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786117,1714256290461786118,1714256290461786120}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786120;
UPDATE "public"."sys_menu" SET "name" = '场景管理', "type" = 'menu', "parent_id" = 1714256290461786117, "order_num" = 76, "url" = '/main/projectManager', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786117,1714256290461786123}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786123;
UPDATE "public"."sys_menu" SET "name" = 'WBS映射', "type" = 'menu', "parent_id" = 1714256290461786117, "order_num" = 77, "url" = '/main/wbsOperate', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786117,1714256290461786124}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_bim' WHERE "id" = 1714256290461786124;
UPDATE "public"."sys_menu" SET "name" = '进度管理', "type" = 'dir', "parent_id" = -1, "order_num" = 78, "url" = '/plan-manager-web', "component" = '', "status" = 1, "icon" = '进度管理', "path" = '{1714256290461786125}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/plan/', "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786125;
UPDATE "public"."sys_menu" SET "name" = '进度计划', "type" = 'menu', "parent_id" = 1714256290461786125, "order_num" = 78, "url" = '/main/schedule/list', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786126}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786126;
UPDATE "public"."sys_menu" SET "name" = '实际进度', "type" = 'menu', "parent_id" = 1714256290461786125, "order_num" = 79, "url" = '/main/actual/list', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786127}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786127;
UPDATE "public"."sys_menu" SET "name" = '进度跟踪', "type" = 'dir', "parent_id" = 1714256290461786125, "order_num" = 80, "url" = '/#', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786128}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786128;
UPDATE "public"."sys_menu" SET "name" = '进度报警', "type" = 'menu', "parent_id" = 1714256290461786128, "order_num" = 80, "url" = '/main/progressTrack/warnList', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786128,1714256290461786129}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786129;
UPDATE "public"."sys_menu" SET "name" = '整改记录', "type" = 'menu', "parent_id" = 1714256290461786128, "order_num" = 81, "url" = '/main/progressTrack/rectificationRecord', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786128,1714256290461786130}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786130;
UPDATE "public"."sys_menu" SET "name" = 'BIM沙盘', "type" = 'menu', "parent_id" = 1714256290461786125, "order_num" = 82, "url" = '/main/sandbox', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1716687262993223720}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1716687262993223720;
UPDATE "public"."sys_menu" SET "name" = '进度报告', "type" = 'dir', "parent_id" = 1714256290461786125, "order_num" = 83, "url" = '/#', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786132}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786132;
UPDATE "public"."sys_menu" SET "name" = '投资进度月报', "type" = 'menu', "parent_id" = 1714256290461786132, "order_num" = 83, "url" = '/main/report/monthInvest', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786132,1714256290461786133}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786133;
UPDATE "public"."sys_menu" SET "name" = '项目进展月报', "type" = 'menu', "parent_id" = 1714256290461786132, "order_num" = 84, "url" = '/main/report/monthProgress', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786132,1714256290461786134}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786134;
UPDATE "public"."sys_menu" SET "name" = '基础设置', "type" = 'dir', "parent_id" = 1714256290461786125, "order_num" = 85, "url" = '/#', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786135}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786135;
UPDATE "public"."sys_menu" SET "name" = '日历模板', "type" = 'menu', "parent_id" = 1714256290461786135, "order_num" = 85, "url" = '/main/baseSettings/calendarTemplate', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786135,1714256290461786136}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786136;
UPDATE "public"."sys_menu" SET "name" = '进度报警', "type" = 'menu', "parent_id" = 1714256290461786135, "order_num" = 86, "url" = '/main/baseSettings/warnRule', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786135,1714256290461786137}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = NULL, "sys_module_code" = 'project_plan' WHERE "id" = 1714256290461786137;
UPDATE "public"."sys_menu" SET "name" = '流程/表单管理', "type" = 'menu', "parent_id" = 1714256290461786135, "order_num" = 87, "url" = '/configuration/formFlowConfig', "component" = '', "status" = 1, "icon" = NULL, "path" = '{1714256290461786125,1714256290461786135,1719265509396353033}', "memo" = '', "is_link" = 0, "sys_module_id" = ********, "redirect" = '/basic-data/', "sys_module_code" = 'project_plan' WHERE "id" = 1719265509396353033;