-- resource_user_role
delete from resource_user_role where id in (select t2.id from (select ru.id, ru.username, ru.res_id, er.tenant_id from resource_user ru join ent_resource er on ru.res_id = er.id) t1 join (select rur.id, rur.res_id, rur.user_id, er.tenant_id from resource_user_role rur join ent_resource er on rur.res_id = er.id) t2 on t1.id = t2.user_id where t1.tenant_id != t2.tenant_id);

-- sys_linkage_setting
delete from sys_linkage_setting where id in (select t2.id from (select ru.id, ru.username, ru.res_id, er.tenant_id from resource_user ru join ent_resource er on ru.res_id = er.id) t1 join (select sls.id, sls.business_id, er.tenant_id from sys_linkage_setting sls join ent_resource er on sls.res_id = er.id) t2 on t1.id = t2.business_id where t1.tenant_id != t2.tenant_id);