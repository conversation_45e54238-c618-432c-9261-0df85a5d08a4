ALTER TABLE "public"."ent_resource"
ALTER COLUMN "type" TYPE varchar(30) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."ent_resource"."type" IS '资源类型 ENT-企业，CMP-公司，PROJECT-项目，OPERATION_PROJECT-运维项目，SECTION-标段，DEPT-部门，GROUP-小组';

ALTER TABLE "public"."resource_user_role"
ALTER COLUMN "res_type" TYPE varchar(30) COLLATE "pg_catalog"."default";

COMMENT ON COLUMN "public"."resource_user_role"."res_type" IS '资源类型';

-- 供应商用户关联表合并至用户组织关联表
INSERT INTO "public"."resource_user_org" ("id", "user_id", "org_id", "post_id", "res_id", "created_by", "created_at", "updated_by", "updated_at", "is_deleted")
select t1.id, t1.user_id, t1.id as org_id, t1.post_id, t1.id as res_id, t1.created_by,t1.created_at,t1.updated_by,t1.updated_at,t1.is_deleted from ent_supplier_user t1;

-- 升级用户组织关联表
update resource_user_org set res_id = org_id;

-- 升级用户表资源id
update resource_user ru set res_id = ruo.res_id from resource_user_org ruo where ru.id = ruo.user_id;

-- 部门小组数据同步至资源表
INSERT INTO "public"."ent_resource" ("id", "parent_id", "name", "type", "order_num", "path", "created_by", "created_at", "updated_by", "updated_at", "is_deleted", "tenant_id") select t1.id, t1.parent_id,t1.name,'dept',t1.order_num,t1.path,t1.created_by,t1.created_at,t1.updated_by,t1.updated_at,t1.is_deleted,t1.path[1] as tenant_id from
    resource_org t1 where t1.is_deleted = 0 and org_type = 'CMP_ORG' and node_type = 'DEPT';

INSERT INTO "public"."ent_resource" ("id", "parent_id", "name", "type", "order_num", "path", "created_by", "created_at", "updated_by", "updated_at", "is_deleted", "tenant_id") select t1.id, t1.parent_id,t1.name,'group',t1.order_num,t1.path,t1.created_by,t1.created_at,t1.updated_by,t1.updated_at,t1.is_deleted,t1.path[1] as tenant_id from
    resource_org t1 where t1.is_deleted = 0 and org_type = 'CMP_ORG' and node_type = 'GROUP';

-- 供应商数据同步至资源表
INSERT INTO "public"."ent_resource" ("id", "parent_id", "name", "type", "order_num", "path", "created_by", "created_at", "updated_by", "updated_at", "is_deleted", "tenant_id") select t1.id, t1.res_id as parent_id, t1.name, 'supplier', 1, ARRAY[t1.res_id, t1.id] as path, t1.created_by,t1.created_at,t1.updated_by,t1.updated_at,t1.is_deleted,t1.res_id as tenant_id from ent_supplier t1 where t1.is_deleted = 0;