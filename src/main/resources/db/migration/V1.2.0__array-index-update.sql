-- 数组索引建立
DROP INDEX if EXISTS "resource_org_path_idx";
CREATE INDEX "resource_org_path_idx" ON "resource_org" USING gin ("path" "pg_catalog"."array_ops");

DROP INDEX if EXISTS "sys_menu_path_idx";
CREATE INDEX "sys_menu_path_idx" ON "sys_menu" USING gin ("path" "pg_catalog"."array_ops");

DROP INDEX if EXISTS "ent_resource_path_idx";
CREATE INDEX "ent_resource_path_idx" ON "ent_resource" USING gin ("path" "pg_catalog"."array_ops");

DROP INDEX if EXISTS "resource_user_role_role_ids_idx";
CREATE INDEX "resource_user_role_role_ids_idx" ON "resource_user_role" USING gin ("role_ids" "pg_catalog"."array_ops");