/*
 Navicat Premium Data Transfer

 Source Server         : ************** Pgsql-SAAS
 Source Server Type    : PostgreSQL
 Source Server Version : 130012 (130012)
 Source Host           : **************:5432
 Source Catalog        : ent_admin
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 130012 (130012)
 File Encoding         : 65001

 Date: 22/03/2024 10:57:32
*/


-- ----------------------------
-- Table structure for tenant_config
-- ----------------------------
CREATE TABLE "public"."tenant_config" (
                                          "id" int8 NOT NULL,
                                          "tenant_id" int8 NOT NULL,
                                          "tenant_config" varchar(1000) COLLATE "pg_catalog"."default" NOT NULL,
                                          "scope" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "public"."tenant_config"."id" IS '主键id';
COMMENT ON COLUMN "public"."tenant_config"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_config"."tenant_config" IS '租户配置';
COMMENT ON COLUMN "public"."tenant_config"."scope" IS '作用域';

-- ----------------------------
-- Indexes structure for table tenant_config
-- ----------------------------
CREATE INDEX "tenant_config_scope_idx" ON "public"."tenant_config" USING btree (
    "scope" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE UNIQUE INDEX "tenant_config_tenant_id_idx" ON "public"."tenant_config" USING btree (
    "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_config
-- ----------------------------
ALTER TABLE "public"."tenant_config" ADD CONSTRAINT "tenant_config_pkey" PRIMARY KEY ("id");
