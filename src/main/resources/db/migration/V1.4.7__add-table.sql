CREATE TABLE "public"."invitation_info" (
                                            "id" int8 NOT NULL,
                                            "res_id" int8 NOT NULL,
                                            "code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                            "created_by" int8 NOT NULL DEFAULT 0,
                                            "created_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                            "updated_by" int8 NOT NULL DEFAULT 0,
                                            "updated_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                            "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."invitation_info"."id" IS '资源id';
COMMENT ON COLUMN "public"."invitation_info"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."invitation_info"."code" IS '邀请码';
COMMENT ON COLUMN "public"."invitation_info"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."invitation_info"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."invitation_info"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."invitation_info"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."invitation_info"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."invitation_info" IS '邀请信息表';

-- ----------------------------
-- Indexes structure for table invitation_info
-- ----------------------------
CREATE INDEX "invitation_info_res_id_idx" ON "public"."invitation_info" USING btree (
    "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Uniques structure for table invitation_info
-- ----------------------------
ALTER TABLE "public"."invitation_info" ADD CONSTRAINT "invitation_info_code_key" UNIQUE ("code");

-- ----------------------------
-- Primary Key structure for table invitation_info
-- ----------------------------
ALTER TABLE "public"."invitation_info" ADD CONSTRAINT "invitation_info_pkey" PRIMARY KEY ("id");

CREATE TABLE "public"."invitation_setting" (
                                               "id" int8 NOT NULL,
                                               "res_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL DEFAULT 0,
                                               "created_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                               "updated_by" int8 NOT NULL DEFAULT 0,
                                               "updated_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                               "is_deleted" int2 NOT NULL DEFAULT 0,
                                               "enable_invitation" bool NOT NULL,
                                               "expired_time_of_day" int4 NOT NULL,
                                               "expired_date" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "public"."invitation_setting"."id" IS '资源id';
COMMENT ON COLUMN "public"."invitation_setting"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."invitation_setting"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."invitation_setting"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."invitation_setting"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."invitation_setting"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."invitation_setting"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."invitation_setting"."enable_invitation" IS '是否启用';
COMMENT ON COLUMN "public"."invitation_setting"."expired_time_of_day" IS '过期天数';
COMMENT ON COLUMN "public"."invitation_setting"."expired_date" IS '过期日期';
COMMENT ON TABLE "public"."invitation_setting" IS '邀请信息表';

-- ----------------------------
-- Indexes structure for table invitation_setting
-- ----------------------------
CREATE INDEX "invitation_setting_res_id_idx" ON "public"."invitation_setting" USING btree (
    "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Uniques structure for table invitation_setting
-- ----------------------------
ALTER TABLE "public"."invitation_setting" ADD CONSTRAINT "invitation_setting_res_id_key" UNIQUE ("res_id");

-- ----------------------------
-- Primary Key structure for table invitation_setting
-- ----------------------------
ALTER TABLE "public"."invitation_setting" ADD CONSTRAINT "invitation_setting_pkey" PRIMARY KEY ("id");