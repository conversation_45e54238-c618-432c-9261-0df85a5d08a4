-- 同步状态
update ent_resource er set is_deleted = ro.is_deleted, name = ro.name from resource_org ro where er.id = ro.id;
update ent_resource er set is_deleted = ep.is_deleted, name = ep.name from ent_project ep where er.id = ep.id;

-- 标段数据同步至资源表
INSERT INTO "public"."ent_resource" ("id", "parent_id", "name", "type", "order_num", "path", "created_by", "created_at", "updated_by", "updated_at", "is_deleted", "tenant_id") select t1.id, t1.res_id, t1.name, 'section', t1.row_number, t1.path, 0, t1.created_at, 0, t1.updated_at, 0, t1.tenant_id from
    (select ps.id, ps.name, ps.res_id, er.path || ps.id as path, er.tenant_id, ROW_NUMBER() OVER(ORDER BY ps.created_at) AS row_number, ps.created_at, ps.updated_at from project_section ps join ent_resource er on ps.res_id = er.id where ps.is_deleted = 0) t1;

-- 删除空角色的用户角色资源关联表
update resource_user_role set is_deleted = 1 where role_ids is null or array_length(role_ids, 1) is null;