/*
 Navicat Premium Data Transfer

 Source Server         : ************** Pgsql-SAAS
 Source Server Type    : PostgreSQL
 Source Server Version : 130012 (130012)
 Source Host           : **************:5432
 Source Catalog        : ent-admin
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 130012 (130012)
 File Encoding         : 65001

 Date: 15/11/2023 20:16:30
*/


-- ----------------------------
-- Sequence structure for city_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."city_id_seq";
CREATE SEQUENCE "public"."city_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for county_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."county_id_seq";
CREATE SEQUENCE "public"."county_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1;

-- ----------------------------
-- Sequence structure for province_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."province_id_seq";
CREATE SEQUENCE "public"."province_id_seq"
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1;

-- ----------------------------
-- Table structure for city
-- ----------------------------
DROP TABLE IF EXISTS "public"."city";
CREATE TABLE "public"."city" (
                                 "id" int4 NOT NULL DEFAULT nextval('city_id_seq'::regclass),
                                 "code" int4 NOT NULL,
                                 "name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                 "province_id" int4 NOT NULL,
                                 "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."city"."id" IS '主键id';
COMMENT ON COLUMN "public"."city"."code" IS '城市编码';
COMMENT ON COLUMN "public"."city"."name" IS '城市名称';
COMMENT ON COLUMN "public"."city"."province_id" IS 'provinceId';
COMMENT ON COLUMN "public"."city"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."city" IS '城市表';

-- ----------------------------
-- Table structure for common_key_value
-- ----------------------------
DROP TABLE IF EXISTS "public"."common_key_value";
CREATE TABLE "public"."common_key_value" (
                                             "id" int8 NOT NULL,
                                             "type" varchar(100) COLLATE "pg_catalog"."default",
                                             "k" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
                                             "v" varchar(500000) COLLATE "pg_catalog"."default" NOT NULL,
                                             "created_by" int8 NOT NULL,
                                             "created_at" timestamp(6) NOT NULL,
                                             "updated_by" int8 NOT NULL,
                                             "updated_at" timestamp(6) NOT NULL,
                                             "is_deleted" int2 NOT NULL,
                                             "res_id" int8 NOT NULL
)
;

COMMENT ON COLUMN "public"."common_key_value"."id" IS '主键id';
COMMENT ON COLUMN "public"."common_key_value"."type" IS '类型(预留字段)';
COMMENT ON COLUMN "public"."common_key_value"."k" IS '键';
COMMENT ON COLUMN "public"."common_key_value"."v" IS '值';
COMMENT ON COLUMN "public"."common_key_value"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."common_key_value"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."common_key_value"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."common_key_value"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."common_key_value"."is_deleted" IS '软删除标志位 0：未删除，1：删除';
COMMENT ON COLUMN "public"."common_key_value"."res_id" IS '资源id';
-- 后续新增
ALTER TABLE "public"."common_key_value" ALTER COLUMN "v" TYPE text COLLATE "pg_catalog"."default" USING "v"::text;

-- ----------------------------
-- Table structure for county
-- ----------------------------
DROP TABLE IF EXISTS "public"."county";
CREATE TABLE "public"."county" (
                                   "id" int4 NOT NULL DEFAULT nextval('county_id_seq'::regclass),
                                   "code" int4 NOT NULL,
                                   "name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                   "city_id" int4 NOT NULL,
                                   "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."county"."id" IS '主键id';
COMMENT ON COLUMN "public"."county"."code" IS '区/县编码';
COMMENT ON COLUMN "public"."county"."name" IS '区/县名称';
COMMENT ON TABLE "public"."county" IS '国家表';

-- ----------------------------
-- Table structure for data_dictionary
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_dictionary";
CREATE TABLE "public"."data_dictionary" (
                                            "id" int8 NOT NULL,
                                            "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                            "key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                            "node_id" int8 NOT NULL,
                                            "description" varchar(255) COLLATE "pg_catalog"."default",
                                            "enable" bool NOT NULL,
                                            "sort" int4 NOT NULL,
                                            "res_id" int8 NOT NULL,
                                            "created_at" timestamp(6) NOT NULL,
                                            "created_by" int8 NOT NULL,
                                            "updated_at" timestamp(6) NOT NULL,
                                            "updated_by" int8 NOT NULL,
                                            "is_deleted" int2 NOT NULL,
                                            "status" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."data_dictionary"."name" IS '字典名称';
COMMENT ON COLUMN "public"."data_dictionary"."key" IS '字典key';
COMMENT ON COLUMN "public"."data_dictionary"."node_id" IS '字典挂载的树节点id';
COMMENT ON COLUMN "public"."data_dictionary"."description" IS '描述';
COMMENT ON COLUMN "public"."data_dictionary"."enable" IS '是否启用';
COMMENT ON COLUMN "public"."data_dictionary"."sort" IS '排序';
COMMENT ON COLUMN "public"."data_dictionary"."status" IS '状态 0 未删除 1-删除';
COMMENT ON TABLE "public"."data_dictionary" IS '数据字典';

-- ----------------------------
-- Table structure for data_dictionary_tree_node
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_dictionary_tree_node";
CREATE TABLE "public"."data_dictionary_tree_node" (
                                                      "id" int8 NOT NULL,
                                                      "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "key" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "parent_id" int8 NOT NULL,
                                                      "description" varchar(255) COLLATE "pg_catalog"."default",
                                                      "sort" int4 NOT NULL,
                                                      "tree_type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "res_id" int8 NOT NULL,
                                                      "created_by" int8 NOT NULL,
                                                      "created_at" timestamp(6) NOT NULL,
                                                      "updated_by" int8 NOT NULL,
                                                      "updated_at" timestamp(0) NOT NULL,
                                                      "is_deleted" int2 NOT NULL,
                                                      "status" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."name" IS '节点名';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."key" IS '节点key';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."parent_id" IS '父节点id';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."description" IS '描述';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."sort" IS '排序';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."tree_type" IS '节点所在树类型';
COMMENT ON COLUMN "public"."data_dictionary_tree_node"."status" IS '0正常 1删除';
COMMENT ON TABLE "public"."data_dictionary_tree_node" IS '数据字典树';

-- ----------------------------
-- Table structure for ent_project
-- ----------------------------
DROP TABLE IF EXISTS "public"."ent_project";
CREATE TABLE "public"."ent_project" (
                                        "id" int8 NOT NULL,
                                        "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                        "short_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                        "code" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                        "status" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                        "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                        "approval_type" varchar(10) COLLATE "pg_catalog"."default",
                                        "nature" varchar(10) COLLATE "pg_catalog"."default",
                                        "approval_num" varchar(200) COLLATE "pg_catalog"."default",
                                        "approval_date" date,
                                        "total_investment" numeric(18,0),
                                        "address" varchar(200) COLLATE "pg_catalog"."default",
                                        "area" numeric(18,4),
                                        "planned_period" int4,
                                        "start_date" date,
                                        "end_date" date,
                                        "res_id" int8 NOT NULL,
                                        "created_by" int8 NOT NULL,
                                        "created_at" timestamp(6) NOT NULL,
                                        "updated_by" int8 NOT NULL,
                                        "updated_at" timestamp(6) NOT NULL,
                                        "is_deleted" int2 NOT NULL,
                                        "profile" varchar(1000) COLLATE "pg_catalog"."default",
                                        "have_section" bool
)
;
COMMENT ON COLUMN "public"."ent_project"."id" IS '项目id同项目资源id';
COMMENT ON COLUMN "public"."ent_project"."name" IS '项目名称';
COMMENT ON COLUMN "public"."ent_project"."short_name" IS '项目简称';
COMMENT ON COLUMN "public"."ent_project"."code" IS '项目编号';
COMMENT ON COLUMN "public"."ent_project"."status" IS '项目状态:1-拟建、2-在建、3-竣工、4-试运营、5-停工';
COMMENT ON COLUMN "public"."ent_project"."type" IS '项目类型，0-基建，1-房建';
COMMENT ON COLUMN "public"."ent_project"."approval_type" IS '立项方式:1-核准、2-备案、3-审批';
COMMENT ON COLUMN "public"."ent_project"."nature" IS '建设性质:0-新建项目、1-扩建项目，2-改建项目';
COMMENT ON COLUMN "public"."ent_project"."approval_num" IS '立项批准文号';
COMMENT ON COLUMN "public"."ent_project"."approval_date" IS '立项批准时间';
COMMENT ON COLUMN "public"."ent_project"."total_investment" IS '总投资(单位:元)';
COMMENT ON COLUMN "public"."ent_project"."address" IS '项目地址';
COMMENT ON COLUMN "public"."ent_project"."area" IS '建筑面积(平方米)';
COMMENT ON COLUMN "public"."ent_project"."planned_period" IS '计划工期';
COMMENT ON COLUMN "public"."ent_project"."start_date" IS '开工时间';
COMMENT ON COLUMN "public"."ent_project"."end_date" IS '完工时间';
COMMENT ON COLUMN "public"."ent_project"."res_id" IS '公司或集团资源id';
COMMENT ON COLUMN "public"."ent_project"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."ent_project"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ent_project"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."ent_project"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."ent_project"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."ent_project"."profile" IS '项目概况';
COMMENT ON COLUMN "public"."ent_project"."have_section" IS '是否有标段';
COMMENT ON TABLE "public"."ent_project" IS '企业项目信息表';

-- ----------------------------
-- Table structure for ent_resource
-- ----------------------------
DROP TABLE IF EXISTS "public"."ent_resource";
CREATE TABLE "public"."ent_resource" (
                                         "id" int8 NOT NULL,
                                         "parent_id" int8 NOT NULL,
                                         "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
                                         "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                         "order_num" int4 NOT NULL,
                                         "path" int8[] NOT NULL,
                                         "created_by" int8 NOT NULL DEFAULT 0,
                                         "created_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                         "updated_by" int8 NOT NULL DEFAULT 0,
                                         "updated_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                         "is_deleted" int2 NOT NULL DEFAULT 0,
                                         "tenant_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."ent_resource"."id" IS '资源id';
COMMENT ON COLUMN "public"."ent_resource"."parent_id" IS '资源父id';
COMMENT ON COLUMN "public"."ent_resource"."name" IS '资源名称';
COMMENT ON COLUMN "public"."ent_resource"."type" IS '资源类型 ENT-企业，CMP-公司，PROJECT-项目';
COMMENT ON COLUMN "public"."ent_resource"."order_num" IS '同级资源顺序';
COMMENT ON COLUMN "public"."ent_resource"."path" IS '全路径';
COMMENT ON COLUMN "public"."ent_resource"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."ent_resource"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ent_resource"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."ent_resource"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."ent_resource"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."ent_resource"."tenant_id" IS '租户id';
COMMENT ON TABLE "public"."ent_resource" IS '企业资源表';

-- ----------------------------
-- Table structure for ent_supplier
-- ----------------------------
DROP TABLE IF EXISTS "public"."ent_supplier";
CREATE TABLE "public"."ent_supplier" (
                                         "id" int8 NOT NULL,
                                         "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                         "unified_social_credit_code" varchar(18) COLLATE "pg_catalog"."default",
                                         "registered_capital" numeric(18,0),
                                         "legal_representative" varchar(20) COLLATE "pg_catalog"."default",
                                         "company_tel" varchar(15) COLLATE "pg_catalog"."default",
                                         "contact_person" varchar(20) COLLATE "pg_catalog"."default",
                                         "mobile" varchar(15) COLLATE "pg_catalog"."default",
                                         "contact_email" varchar(50) COLLATE "pg_catalog"."default",
                                         "unit_nature" varchar(30) COLLATE "pg_catalog"."default",
                                         "unit_type" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                         "unit_profile" varchar(1000) COLLATE "pg_catalog"."default",
                                         "establish_time" date,
                                         "issuing_authority" varchar(200) COLLATE "pg_catalog"."default",
                                         "social_security_authority" varchar(200) COLLATE "pg_catalog"."default",
                                         "reg_addr" varchar(200) COLLATE "pg_catalog"."default",
                                         "reg_type" varchar(200) COLLATE "pg_catalog"."default",
                                         "manage_scope" varchar(1000) COLLATE "pg_catalog"."default",
                                         "manage_addr" varchar(200) COLLATE "pg_catalog"."default",
                                         "postal_code" varchar(10) COLLATE "pg_catalog"."default",
                                         "website" varchar(200) COLLATE "pg_catalog"."default",
                                         "province" varchar(100) COLLATE "pg_catalog"."default",
                                         "res_id" int8 NOT NULL,
                                         "created_by" int8 NOT NULL,
                                         "created_at" timestamp(6) NOT NULL,
                                         "updated_by" int8 NOT NULL,
                                         "updated_at" timestamp(6) NOT NULL,
                                         "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."ent_supplier"."id" IS '供应商id';
COMMENT ON COLUMN "public"."ent_supplier"."name" IS '供应商名称';
COMMENT ON COLUMN "public"."ent_supplier"."unified_social_credit_code" IS '统一信用代码';
COMMENT ON COLUMN "public"."ent_supplier"."registered_capital" IS '注册资本';
COMMENT ON COLUMN "public"."ent_supplier"."legal_representative" IS '法人代表';
COMMENT ON COLUMN "public"."ent_supplier"."company_tel" IS '单位电话';
COMMENT ON COLUMN "public"."ent_supplier"."contact_person" IS '联系人';
COMMENT ON COLUMN "public"."ent_supplier"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."ent_supplier"."contact_email" IS '联系邮箱';
COMMENT ON COLUMN "public"."ent_supplier"."unit_nature" IS '单位性质';
COMMENT ON COLUMN "public"."ent_supplier"."unit_type" IS '单位类型';
COMMENT ON COLUMN "public"."ent_supplier"."unit_profile" IS '单位概况';
COMMENT ON COLUMN "public"."ent_supplier"."establish_time" IS '成立时间';
COMMENT ON COLUMN "public"."ent_supplier"."issuing_authority" IS '发证机关';
COMMENT ON COLUMN "public"."ent_supplier"."social_security_authority" IS '社保登记机关';
COMMENT ON COLUMN "public"."ent_supplier"."reg_addr" IS '注册地址';
COMMENT ON COLUMN "public"."ent_supplier"."reg_type" IS '注册类型';
COMMENT ON COLUMN "public"."ent_supplier"."manage_scope" IS '经营范围';
COMMENT ON COLUMN "public"."ent_supplier"."manage_addr" IS '经营地址';
COMMENT ON COLUMN "public"."ent_supplier"."postal_code" IS '邮政编码';
COMMENT ON COLUMN "public"."ent_supplier"."website" IS '单位网址';
COMMENT ON COLUMN "public"."ent_supplier"."province" IS '省份';
COMMENT ON COLUMN "public"."ent_supplier"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."ent_supplier"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."ent_supplier"."created_at" IS '创建人id';
COMMENT ON COLUMN "public"."ent_supplier"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."ent_supplier"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."ent_supplier"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."ent_supplier" IS '供应商信息表';

-- ----------------------------
-- Table structure for ent_supplier_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."ent_supplier_user";
CREATE TABLE "public"."ent_supplier_user" (
                                              "id" int8 NOT NULL,
                                              "user_id" int8 NOT NULL,
                                              "supplier_id" int8 NOT NULL,
                                              "post_id" int8,
                                              "res_id" int8 NOT NULL,
                                              "created_by" int8 NOT NULL,
                                              "created_at" timestamp(6) NOT NULL,
                                              "updated_by" int8 NOT NULL,
                                              "updated_at" timestamp(6) NOT NULL,
                                              "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."ent_supplier_user"."id" IS '供应商用户关联id';
COMMENT ON COLUMN "public"."ent_supplier_user"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."ent_supplier_user"."supplier_id" IS '供应商id';
COMMENT ON COLUMN "public"."ent_supplier_user"."post_id" IS '职务id';
COMMENT ON COLUMN "public"."ent_supplier_user"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."ent_supplier_user"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."ent_supplier_user"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ent_supplier_user"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."ent_supplier_user"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."ent_supplier_user"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."ent_supplier_user" IS '供应商用户关联表';

-- ----------------------------
-- Table structure for person_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."person_detail";
CREATE TABLE "public"."person_detail" (
                                          "id" int8 NOT NULL,
                                          "person_id" int8 NOT NULL,
                                          "person_type" int2 NOT NULL,
                                          "gender" int2,
                                          "age" int2,
                                          "birth_date" date,
                                          "nation" varchar(15) COLLATE "pg_catalog"."default",
                                          "education" int2,
                                          "id_card_front_pic" varchar(32) COLLATE "pg_catalog"."default",
                                          "id_card_back_pic" varchar(32) COLLATE "pg_catalog"."default",
                                          "id_pic" varchar(32) COLLATE "pg_catalog"."default",
                                          "remark" varchar(500) COLLATE "pg_catalog"."default",
                                          "id_card" varchar(18) COLLATE "pg_catalog"."default",
                                          "id_card_valid_date" date,
                                          "political_status" varchar(32) COLLATE "pg_catalog"."default",
                                          "native_place" varchar(50) COLLATE "pg_catalog"."default",
                                          "issuance_unit" varchar(100) COLLATE "pg_catalog"."default",
                                          "emergency_person" varchar(50) COLLATE "pg_catalog"."default",
                                          "emergency_mobile" varchar(15) COLLATE "pg_catalog"."default",
                                          "address" varchar(500) COLLATE "pg_catalog"."default",
                                          "bank_name" varchar(50) COLLATE "pg_catalog"."default",
                                          "bank_branch_name" varchar(50) COLLATE "pg_catalog"."default",
                                          "bank_card_no" varchar(50) COLLATE "pg_catalog"."default",
                                          "bank_account_no" varchar(50) COLLATE "pg_catalog"."default",
                                          "res_id" int8,
                                          "created_by" int8,
                                          "created_at" timestamp(6),
                                          "updated_by" int8,
                                          "updated_at" timestamp(6),
                                          "is_deleted" int2 DEFAULT 0,
                                          "work_year" int2
)
;
COMMENT ON COLUMN "public"."person_detail"."id" IS '用户详情id';
COMMENT ON COLUMN "public"."person_detail"."person_id" IS '用户id';
COMMENT ON COLUMN "public"."person_detail"."person_type" IS '人员类型，0-系统人员， 1-作业人员';
COMMENT ON COLUMN "public"."person_detail"."gender" IS '性别，0-男，1-女';
COMMENT ON COLUMN "public"."person_detail"."age" IS '年龄';
COMMENT ON COLUMN "public"."person_detail"."birth_date" IS '出生日期';
COMMENT ON COLUMN "public"."person_detail"."nation" IS '民族';
COMMENT ON COLUMN "public"."person_detail"."education" IS '学历 0-文盲,1-小学,2-中学,3-中专,4-高中,5-大专,6-本科,7-硕士,8-博士';
COMMENT ON COLUMN "public"."person_detail"."id_card_front_pic" IS '身份证正面文件id';
COMMENT ON COLUMN "public"."person_detail"."id_card_back_pic" IS '身份证反面文件id';
COMMENT ON COLUMN "public"."person_detail"."id_pic" IS '证件照';
COMMENT ON COLUMN "public"."person_detail"."remark" IS '备注';
COMMENT ON COLUMN "public"."person_detail"."id_card" IS '身份证号';
COMMENT ON COLUMN "public"."person_detail"."id_card_valid_date" IS '身份证有效期';
COMMENT ON COLUMN "public"."person_detail"."political_status" IS '政治面貌';
COMMENT ON COLUMN "public"."person_detail"."native_place" IS '籍贯';
COMMENT ON COLUMN "public"."person_detail"."issuance_unit" IS '发证单位';
COMMENT ON COLUMN "public"."person_detail"."emergency_person" IS '紧急联系人';
COMMENT ON COLUMN "public"."person_detail"."emergency_mobile" IS '紧急联系电话';
COMMENT ON COLUMN "public"."person_detail"."address" IS '地址';
COMMENT ON COLUMN "public"."person_detail"."bank_name" IS '银行名称';
COMMENT ON COLUMN "public"."person_detail"."bank_branch_name" IS '分行名称';
COMMENT ON COLUMN "public"."person_detail"."bank_card_no" IS '银行卡号';
COMMENT ON COLUMN "public"."person_detail"."bank_account_no" IS '银行联号';
COMMENT ON COLUMN "public"."person_detail"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."person_detail"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."person_detail"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."person_detail"."updated_by" IS '更新人id';
COMMENT ON COLUMN "public"."person_detail"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."person_detail"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."person_detail"."work_year" IS '工作年限';
COMMENT ON TABLE "public"."person_detail" IS '人员补充信息表';

-- ----------------------------
-- Table structure for person_work_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."person_work_info";
CREATE TABLE "public"."person_work_info" (
                                             "id" int8 NOT NULL,
                                             "person_id" int8 NOT NULL,
                                             "work_area_id" int8,
                                             "hold_certificate_status" int2,
                                             "presence_status" int2,
                                             "entry_date" date,
                                             "exit_date" date,
                                             "is_safety_education" int2,
                                             "is_insurance_status" int2,
                                             "is_medical_history" int2,
                                             "is_occupational_disease" int2,
                                             "occupational_disease_name" varchar(400) COLLATE "pg_catalog"."default",
                                             "person_type" int2 NOT NULL,
                                             "res_id" int8 NOT NULL,
                                             "created_by" int8 NOT NULL,
                                             "created_at" timestamp(6) NOT NULL,
                                             "updated_by" int8 NOT NULL,
                                             "updated_at" timestamp(6) NOT NULL,
                                             "is_deleted" int2 NOT NULL,
                                             "section_id" int8,
                                             "person_code" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."person_work_info"."id" IS '工作id';
COMMENT ON COLUMN "public"."person_work_info"."person_id" IS '人员id';
COMMENT ON COLUMN "public"."person_work_info"."work_area_id" IS '工作区域id';
COMMENT ON COLUMN "public"."person_work_info"."hold_certificate_status" IS '持证情况： 0-无需持证， 1-需持证';
COMMENT ON COLUMN "public"."person_work_info"."presence_status" IS '在场状态： 0-退场， 1-进场';
COMMENT ON COLUMN "public"."person_work_info"."entry_date" IS '进场日期';
COMMENT ON COLUMN "public"."person_work_info"."exit_date" IS '出场日期';
COMMENT ON COLUMN "public"."person_work_info"."is_safety_education" IS '是否参加安全教育：0-不参加，1-参加';
COMMENT ON COLUMN "public"."person_work_info"."is_insurance_status" IS '是否购买保险： 0-不购买，1-购买';
COMMENT ON COLUMN "public"."person_work_info"."is_medical_history" IS '是否有重大病史：0-无，1-有';
COMMENT ON COLUMN "public"."person_work_info"."is_occupational_disease" IS '是否有职业病：0-无，1-有';
COMMENT ON COLUMN "public"."person_work_info"."occupational_disease_name" IS '职业病名称';
COMMENT ON COLUMN "public"."person_work_info"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."person_work_info"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."person_work_info"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."person_work_info"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."person_work_info"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."person_work_info"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."person_work_info"."section_id" IS '标段id';
COMMENT ON COLUMN "public"."person_work_info"."person_code" IS '人员编号';
COMMENT ON TABLE "public"."person_work_info" IS '人员工作信息表';

-- ----------------------------
-- Table structure for process_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."process_type";
CREATE TABLE "public"."process_type" (
                                         "id" int8 NOT NULL,
                                         "res_id" int8 NOT NULL,
                                         "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                         "key" varchar(200) COLLATE "pg_catalog"."default",
                                         "pid" int8 NOT NULL DEFAULT 0,
                                         "type" int4 NOT NULL,
                                         "sys_module_code" varchar(200) COLLATE "pg_catalog"."default",
                                         "description" varchar(200) COLLATE "pg_catalog"."default",
                                         "enable" int2 DEFAULT 1,
                                         "sort" int4 NOT NULL DEFAULT 0,
                                         "created_by" int8 NOT NULL,
                                         "created_at" timestamp(6) NOT NULL,
                                         "updated_by" int8 NOT NULL,
                                         "updated_at" timestamp(6) NOT NULL,
                                         "is_deleted" int2 NOT NULL DEFAULT 0,
                                         "data_type" int4 NOT NULL DEFAULT 0,
                                         "json" varchar(500) COLLATE "pg_catalog"."default",
                                         "app_type" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."process_type"."id" IS '主键';
COMMENT ON COLUMN "public"."process_type"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."process_type"."name" IS '名称';
COMMENT ON COLUMN "public"."process_type"."key" IS 'key';
COMMENT ON COLUMN "public"."process_type"."pid" IS '所属父节点id，顶级节点为0';
COMMENT ON COLUMN "public"."process_type"."type" IS '节点类型：0=数据目录，1=数据类型';
COMMENT ON COLUMN "public"."process_type"."sys_module_code" IS '系统编码：具体使用于那个系统。type=1=数据类型时不能为空';
COMMENT ON COLUMN "public"."process_type"."description" IS '描述';
COMMENT ON COLUMN "public"."process_type"."enable" IS '是否启用';
COMMENT ON COLUMN "public"."process_type"."sort" IS '排序';
COMMENT ON COLUMN "public"."process_type"."is_deleted" IS '是否删除';
COMMENT ON COLUMN "public"."process_type"."data_type" IS '数据类型：0=系统；1=模块；2=菜单；';
COMMENT ON COLUMN "public"."process_type"."json" IS 'json字符串';
COMMENT ON COLUMN "public"."process_type"."app_type" IS '应用类型：1=流程；2=任务；3=信息';

-- ----------------------------
-- Table structure for project_participant
-- ----------------------------
DROP TABLE IF EXISTS "public"."project_participant";
CREATE TABLE "public"."project_participant" (
                                                "id" int8 NOT NULL,
                                                "participant_unit_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                                "section_id" int8,
                                                "participant_unit_id" int8 NOT NULL,
                                                "unit_type" varchar(30) COLLATE "pg_catalog"."default",
                                                "contact_name" varchar(20) COLLATE "pg_catalog"."default",
                                                "contact_phone" varchar(15) COLLATE "pg_catalog"."default",
                                                "res_id" int8 NOT NULL,
                                                "created_by" int8 NOT NULL,
                                                "created_at" timestamp(6) NOT NULL,
                                                "updated_by" int8 NOT NULL,
                                                "updated_at" timestamp(6) NOT NULL,
                                                "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."project_participant"."id" IS '项目参建单位关联id';
COMMENT ON COLUMN "public"."project_participant"."participant_unit_type" IS '参建单位类型';
COMMENT ON COLUMN "public"."project_participant"."section_id" IS '标段id(为空说明是项目层级的数据)';
COMMENT ON COLUMN "public"."project_participant"."participant_unit_id" IS '参建单位id';
COMMENT ON COLUMN "public"."project_participant"."unit_type" IS '单位类型';
COMMENT ON COLUMN "public"."project_participant"."contact_name" IS '联系人名称';
COMMENT ON COLUMN "public"."project_participant"."contact_phone" IS '联系号码';
COMMENT ON COLUMN "public"."project_participant"."res_id" IS '项目资源id';
COMMENT ON COLUMN "public"."project_participant"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_participant"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_participant"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_participant"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_participant"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."project_participant" IS '项目参建单位关联表';

-- ----------------------------
-- Table structure for project_section
-- ----------------------------
DROP TABLE IF EXISTS "public"."project_section";
CREATE TABLE "public"."project_section" (
                                            "id" int8 NOT NULL,
                                            "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                            "short_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                            "code" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                            "contract_code" varchar(200) COLLATE "pg_catalog"."default",
                                            "contract_amount" numeric(18,0),
                                            "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                            "rate" numeric(8,4),
                                            "base_bid_budget" numeric(18,0),
                                            "start_date" date,
                                            "end_date" date,
                                            "res_id" int8 NOT NULL,
                                            "created_by" int8 NOT NULL,
                                            "created_at" timestamp(6) NOT NULL,
                                            "updated_by" int8 NOT NULL,
                                            "updated_at" timestamp(6) NOT NULL,
                                            "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."project_section"."id" IS '标段id';
COMMENT ON COLUMN "public"."project_section"."name" IS '标段名称';
COMMENT ON COLUMN "public"."project_section"."short_name" IS '标段简称';
COMMENT ON COLUMN "public"."project_section"."code" IS '标段号';
COMMENT ON COLUMN "public"."project_section"."contract_code" IS '合同编号';
COMMENT ON COLUMN "public"."project_section"."contract_amount" IS '合同金额';
COMMENT ON COLUMN "public"."project_section"."type" IS '类别';
COMMENT ON COLUMN "public"."project_section"."rate" IS '费率';
COMMENT ON COLUMN "public"."project_section"."base_bid_budget" IS '标底预算';
COMMENT ON COLUMN "public"."project_section"."start_date" IS '开工时间';
COMMENT ON COLUMN "public"."project_section"."end_date" IS '完工时间';
COMMENT ON COLUMN "public"."project_section"."res_id" IS '项目资源id';
COMMENT ON COLUMN "public"."project_section"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_section"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_section"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_section"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_section"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."project_section" IS '项目标段信息表';

-- ----------------------------
-- Table structure for project_user_org
-- ----------------------------
DROP TABLE IF EXISTS "public"."project_user_org";
CREATE TABLE "public"."project_user_org" (
                                             "id" int8 NOT NULL,
                                             "user_id" int8 NOT NULL,
                                             "org_id" int8 NOT NULL,
                                             "post_id" int8,
                                             "res_id" int8 NOT NULL,
                                             "created_by" int8 NOT NULL,
                                             "created_at" timestamp(6) NOT NULL,
                                             "updated_by" int8 NOT NULL,
                                             "updated_at" timestamp(6) NOT NULL,
                                             "is_deleted" int2 NOT NULL,
                                             "deleted_at" int8 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."project_user_org"."id" IS '项目人员关联id';
COMMENT ON COLUMN "public"."project_user_org"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."project_user_org"."org_id" IS '项目组织id';
COMMENT ON COLUMN "public"."project_user_org"."post_id" IS '职务id';
COMMENT ON COLUMN "public"."project_user_org"."res_id" IS '项目资源id';
COMMENT ON COLUMN "public"."project_user_org"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_user_org"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_user_org"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_user_org"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_user_org"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."project_user_org"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."project_user_org" IS '项目人员组织关联表';

-- ----------------------------
-- Table structure for project_user_section
-- ----------------------------
DROP TABLE IF EXISTS "public"."project_user_section";
CREATE TABLE "public"."project_user_section" (
                                                 "id" int8 NOT NULL,
                                                 "user_id" int8 NOT NULL,
                                                 "section_id" int8,
                                                 "res_id" int8 NOT NULL,
                                                 "created_by" int8 NOT NULL,
                                                 "created_at" timestamp(6) NOT NULL,
                                                 "updated_by" int8 NOT NULL,
                                                 "updated_at" timestamp(6) NOT NULL,
                                                 "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."project_user_section"."id" IS '项目人员标段关联id';
COMMENT ON COLUMN "public"."project_user_section"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."project_user_section"."section_id" IS '标段id';
COMMENT ON COLUMN "public"."project_user_section"."res_id" IS '项目资源id';
COMMENT ON COLUMN "public"."project_user_section"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_user_section"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_user_section"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_user_section"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_user_section"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."project_user_section" IS '项目人员标段关联表';

-- ----------------------------
-- Table structure for province
-- ----------------------------
DROP TABLE IF EXISTS "public"."province";
CREATE TABLE "public"."province" (
                                     "id" int4 NOT NULL DEFAULT nextval('province_id_seq'::regclass),
                                     "code" int4 NOT NULL,
                                     "name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                     "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."province"."id" IS '主键id';
COMMENT ON COLUMN "public"."province"."code" IS '省份编码';
COMMENT ON COLUMN "public"."province"."name" IS '省份名称';
COMMENT ON COLUMN "public"."province"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."province" IS '省份表';

-- ----------------------------
-- Table structure for resource_attachment
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_attachment";
CREATE TABLE "public"."resource_attachment" (
                                                "id" int8 NOT NULL,
                                                "relation_id" int8 NOT NULL,
                                                "relation_type" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                                "file_uuid" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "file_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                                "file_size" int8,
                                                "file_md5" varchar(32) COLLATE "pg_catalog"."default",
                                                "res_id" int8 NOT NULL,
                                                "created_by" int8 NOT NULL,
                                                "created_at" timestamp(6) NOT NULL,
                                                "updated_by" int8 NOT NULL,
                                                "updated_at" timestamp(6) NOT NULL,
                                                "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_attachment"."id" IS '附件id';
COMMENT ON COLUMN "public"."resource_attachment"."relation_id" IS '附件关联业务id';
COMMENT ON COLUMN "public"."resource_attachment"."relation_type" IS '附件关联业务类型';
COMMENT ON COLUMN "public"."resource_attachment"."file_uuid" IS '文件uuid';
COMMENT ON COLUMN "public"."resource_attachment"."file_name" IS '文件名';
COMMENT ON COLUMN "public"."resource_attachment"."file_size" IS '文件大小';
COMMENT ON COLUMN "public"."resource_attachment"."file_md5" IS '文件md5';
COMMENT ON COLUMN "public"."resource_attachment"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_attachment"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_attachment"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_attachment"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_attachment"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_attachment"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_attachment" IS '附件表';

-- ----------------------------
-- Table structure for resource_org
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_org";
CREATE TABLE "public"."resource_org" (
                                         "id" int8 NOT NULL,
                                         "parent_id" int8 NOT NULL,
                                         "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                         "node_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                         "order_num" int4 NOT NULL,
                                         "org_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                         "contact_person" varchar(20) COLLATE "pg_catalog"."default",
                                         "mobile" varchar(15) COLLATE "pg_catalog"."default",
                                         "fax" varchar(10) COLLATE "pg_catalog"."default",
                                         "address" varchar(200) COLLATE "pg_catalog"."default",
                                         "memo" varchar(200) COLLATE "pg_catalog"."default",
                                         "res_id" int8 NOT NULL,
                                         "created_by" int8 NOT NULL DEFAULT 0,
                                         "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                         "updated_by" int8 NOT NULL DEFAULT 0,
                                         "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                         "is_deleted" int2 NOT NULL DEFAULT 0,
                                         "path" int8[] NOT NULL,
                                         "belong_res_id" int8
)
;
COMMENT ON COLUMN "public"."resource_org"."id" IS '节点id';
COMMENT ON COLUMN "public"."resource_org"."parent_id" IS '节点父id';
COMMENT ON COLUMN "public"."resource_org"."name" IS '组织名称';
COMMENT ON COLUMN "public"."resource_org"."node_type" IS '节点类型';
COMMENT ON COLUMN "public"."resource_org"."order_num" IS '同级资源顺序';
COMMENT ON COLUMN "public"."resource_org"."org_type" IS '组织类型';
COMMENT ON COLUMN "public"."resource_org"."contact_person" IS '联系人';
COMMENT ON COLUMN "public"."resource_org"."mobile" IS '联系电话';
COMMENT ON COLUMN "public"."resource_org"."fax" IS '传真';
COMMENT ON COLUMN "public"."resource_org"."address" IS '联系地址';
COMMENT ON COLUMN "public"."resource_org"."memo" IS '备注';
COMMENT ON COLUMN "public"."resource_org"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_org"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_org"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_org"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_org"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_org"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."resource_org"."path" IS '全路径';
COMMENT ON COLUMN "public"."resource_org"."belong_res_id" IS '节点所属资源id(离自己最近的资源id)';
COMMENT ON TABLE "public"."resource_org" IS '资源组织结构表';

-- ----------------------------
-- Table structure for resource_package_auth
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_package_auth";
CREATE TABLE "public"."resource_package_auth" (
                                                  "id" int8 NOT NULL,
                                                  "user_id" int8 NOT NULL,
                                                  "package_id" int8 NOT NULL,
                                                  "res_id" int8 NOT NULL,
                                                  "created_by" int8 NOT NULL,
                                                  "created_at" timestamp(6) NOT NULL,
                                                  "updated_by" int8 NOT NULL,
                                                  "updated_at" timestamp(6) NOT NULL,
                                                  "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_package_auth"."id" IS '授权id';
COMMENT ON COLUMN "public"."resource_package_auth"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."resource_package_auth"."package_id" IS '套餐id';
COMMENT ON COLUMN "public"."resource_package_auth"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_package_auth"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_package_auth"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_package_auth"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_package_auth"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_package_auth"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_package_auth" IS '资源套餐授权表';

-- ----------------------------
-- Table structure for resource_post
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_post";
CREATE TABLE "public"."resource_post" (
                                          "id" int8 NOT NULL,
                                          "post_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                          "memo" varchar(200) COLLATE "pg_catalog"."default",
                                          "res_id" int8 NOT NULL,
                                          "created_by" int8 NOT NULL,
                                          "created_at" timestamp(6) NOT NULL,
                                          "updated_by" int8 NOT NULL,
                                          "updated_at" timestamp(6) NOT NULL,
                                          "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_post"."id" IS '职务id';
COMMENT ON COLUMN "public"."resource_post"."post_name" IS '职务名称';
COMMENT ON COLUMN "public"."resource_post"."memo" IS '备注';
COMMENT ON COLUMN "public"."resource_post"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_post"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_post"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_post"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_post"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_post"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_post" IS '资源职务表';

-- ----------------------------
-- Table structure for resource_post_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_post_role";
CREATE TABLE "public"."resource_post_role" (
                                               "id" int8 NOT NULL,
                                               "role_id" int8 NOT NULL,
                                               "post_id" int8 NOT NULL,
                                               "res_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL,
                                               "created_at" timestamp(6) NOT NULL,
                                               "updated_by" int8 NOT NULL,
                                               "updated_at" timestamp(6) NOT NULL,
                                               "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_post_role"."id" IS '职务角色关联id';
COMMENT ON COLUMN "public"."resource_post_role"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."resource_post_role"."post_id" IS '职务id';
COMMENT ON COLUMN "public"."resource_post_role"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_post_role"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_post_role"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_post_role"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_post_role"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_post_role"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_post_role" IS '职务角色关联表';

-- ----------------------------
-- Table structure for resource_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_role";
CREATE TABLE "public"."resource_role" (
                                          "id" int8 NOT NULL,
                                          "role_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
                                          "memo" varchar(200) COLLATE "pg_catalog"."default",
                                          "res_id" int8 NOT NULL,
                                          "created_by" int8 NOT NULL,
                                          "created_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                          "updated_by" int8 NOT NULL DEFAULT 0,
                                          "updated_at" timestamp(6) NOT NULL DEFAULT LOCALTIMESTAMP,
                                          "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."resource_role"."id" IS '角色id';
COMMENT ON COLUMN "public"."resource_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."resource_role"."memo" IS '备注';
COMMENT ON COLUMN "public"."resource_role"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_role"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_role"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_role"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_role"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_role"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_role" IS '资源角色表';

-- ----------------------------
-- Table structure for resource_role_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_role_menu";
CREATE TABLE "public"."resource_role_menu" (
                                               "id" int8 NOT NULL,
                                               "role_id" int8 NOT NULL,
                                               "menu_id" int8 NOT NULL,
                                               "res_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL,
                                               "created_at" timestamp(6) NOT NULL,
                                               "updated_by" int8 NOT NULL,
                                               "updated_at" timestamp(6) NOT NULL,
                                               "is_deleted" int2 NOT NULL,
                                               "sys_module_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_role_menu"."id" IS '角色菜单关联id';
COMMENT ON COLUMN "public"."resource_role_menu"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."resource_role_menu"."menu_id" IS '菜单id';
COMMENT ON COLUMN "public"."resource_role_menu"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_role_menu"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_role_menu"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_role_menu"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_role_menu"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_role_menu"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."resource_role_menu"."sys_module_id" IS '系统模块id';
COMMENT ON TABLE "public"."resource_role_menu" IS '资源角色菜单关联表';

-- ----------------------------
-- Table structure for resource_role_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_role_permission";
CREATE TABLE "public"."resource_role_permission" (
                                                     "id" int8 NOT NULL,
                                                     "role_id" int8 NOT NULL,
                                                     "permission_id" int8 NOT NULL,
                                                     "res_id" int8 NOT NULL,
                                                     "created_by" int8 NOT NULL,
                                                     "created_at" timestamp(6) NOT NULL,
                                                     "updated_by" int8 NOT NULL,
                                                     "updated_at" timestamp(6) NOT NULL,
                                                     "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_role_permission"."id" IS '角色权限关联id';
COMMENT ON COLUMN "public"."resource_role_permission"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."resource_role_permission"."permission_id" IS '权限id';
COMMENT ON COLUMN "public"."resource_role_permission"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_role_permission"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_role_permission"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_role_permission"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_role_permission"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_role_permission"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_role_permission" IS '角色权限关联表';

-- ----------------------------
-- Table structure for resource_supplier_ref
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_supplier_ref";
CREATE TABLE "public"."resource_supplier_ref" (
                                                  "id" int8 NOT NULL,
                                                  "supplier_id" int8 NOT NULL,
                                                  "res_id" int8 NOT NULL,
                                                  "created_by" int8 NOT NULL,
                                                  "created_at" timestamp(6) NOT NULL,
                                                  "updated_by" int8 NOT NULL,
                                                  "updated_at" timestamp(6) NOT NULL,
                                                  "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_supplier_ref"."id" IS '资源供应商关联id';
COMMENT ON COLUMN "public"."resource_supplier_ref"."supplier_id" IS '供应商id';
COMMENT ON COLUMN "public"."resource_supplier_ref"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_supplier_ref"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_supplier_ref"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_supplier_ref"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_supplier_ref"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_supplier_ref"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_supplier_ref" IS '资源供应商关联表';

-- ----------------------------
-- Table structure for resource_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_user";
CREATE TABLE "public"."resource_user" (
                                          "id" int8 NOT NULL,
                                          "username" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                          "real_name" varchar(20) COLLATE "pg_catalog"."default",
                                          "email" varchar(50) COLLATE "pg_catalog"."default",
                                          "mobile" varchar(15) COLLATE "pg_catalog"."default",
                                          "type" int2 NOT NULL,
                                          "res_id" int8 NOT NULL,
                                          "created_by" int8 NOT NULL,
                                          "created_at" timestamp(6) NOT NULL,
                                          "updated_by" int8 NOT NULL,
                                          "updated_at" timestamp(6) NOT NULL,
                                          "is_deleted" int2 NOT NULL,
                                          "status" int2
)
;
COMMENT ON COLUMN "public"."resource_user"."id" IS '用户id';
COMMENT ON COLUMN "public"."resource_user"."username" IS '账号';
COMMENT ON COLUMN "public"."resource_user"."real_name" IS '真实姓名';
COMMENT ON COLUMN "public"."resource_user"."email" IS '邮箱';
COMMENT ON COLUMN "public"."resource_user"."mobile" IS '手机号';
COMMENT ON COLUMN "public"."resource_user"."type" IS '0-内部人员，1-外部人员';
COMMENT ON COLUMN "public"."resource_user"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_user"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_user"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_user"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_user"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_user"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."resource_user"."status" IS '人员状态，0：正常， 1：冻结';
COMMENT ON TABLE "public"."resource_user" IS '资源用户表';

-- ----------------------------
-- Table structure for resource_user_credentials
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_user_credentials";
CREATE TABLE "public"."resource_user_credentials" (
                                                      "id" int8 NOT NULL,
                                                      "user_id" int8 NOT NULL,
                                                      "credentials_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "credentials_code" varchar(200) COLLATE "pg_catalog"."default",
                                                      "certification_level" varchar(200) COLLATE "pg_catalog"."default",
                                                      "certification_authority" varchar(200) COLLATE "pg_catalog"."default",
                                                      "issue_date" date,
                                                      "res_id" int8 NOT NULL,
                                                      "created_by" int8 NOT NULL,
                                                      "created_at" timestamp(6) NOT NULL,
                                                      "updated_by" int8 NOT NULL,
                                                      "updated_at" timestamp(6) NOT NULL,
                                                      "is_deleted" int2 NOT NULL,
                                                      "expiry_date" date
)
;
COMMENT ON COLUMN "public"."resource_user_credentials"."id" IS '证件id';
COMMENT ON COLUMN "public"."resource_user_credentials"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."resource_user_credentials"."credentials_name" IS '证书名称';
COMMENT ON COLUMN "public"."resource_user_credentials"."credentials_code" IS '证书编号';
COMMENT ON COLUMN "public"."resource_user_credentials"."certification_level" IS '认证级别';
COMMENT ON COLUMN "public"."resource_user_credentials"."certification_authority" IS '发证机构';
COMMENT ON COLUMN "public"."resource_user_credentials"."issue_date" IS '出证日期';
COMMENT ON COLUMN "public"."resource_user_credentials"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_user_credentials"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_user_credentials"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_user_credentials"."updated_by" IS '更新人id';
COMMENT ON COLUMN "public"."resource_user_credentials"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."resource_user_credentials"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."resource_user_credentials"."expiry_date" IS '证件失效日期';
COMMENT ON TABLE "public"."resource_user_credentials" IS '用户证书表';

-- ----------------------------
-- Table structure for resource_user_org
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_user_org";
CREATE TABLE "public"."resource_user_org" (
                                              "id" int8 NOT NULL,
                                              "user_id" int8 NOT NULL,
                                              "org_id" int8 NOT NULL,
                                              "post_id" int8,
                                              "res_id" int8 NOT NULL,
                                              "created_by" int8 NOT NULL,
                                              "created_at" timestamp(6) NOT NULL,
                                              "updated_by" int8 NOT NULL,
                                              "updated_at" timestamp(6) NOT NULL,
                                              "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_user_org"."id" IS '资源用户组织关联id';
COMMENT ON COLUMN "public"."resource_user_org"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."resource_user_org"."org_id" IS '组织结构id 对应resource_org的id';
COMMENT ON COLUMN "public"."resource_user_org"."post_id" IS '职务id';
COMMENT ON COLUMN "public"."resource_user_org"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_user_org"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_user_org"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_user_org"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_user_org"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_user_org"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_user_org" IS '资源用户组织表';

-- ----------------------------
-- Table structure for resource_user_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."resource_user_role";
CREATE TABLE "public"."resource_user_role" (
                                               "id" int8 NOT NULL,
                                               "user_id" int8 NOT NULL,
                                               "role_ids" int8[],
                                               "res_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL,
                                               "created_at" timestamp(6) NOT NULL,
                                               "updated_by" int8 NOT NULL,
                                               "updated_at" timestamp(6) NOT NULL,
                                               "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."resource_user_role"."id" IS '用户角色关联id';
COMMENT ON COLUMN "public"."resource_user_role"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."resource_user_role"."role_ids" IS '角色ids';
COMMENT ON COLUMN "public"."resource_user_role"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."resource_user_role"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."resource_user_role"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."resource_user_role"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."resource_user_role"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."resource_user_role"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."resource_user_role" IS '资源用户角色关联表';

-- ----------------------------
-- Table structure for staff_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."staff_info";
CREATE TABLE "public"."staff_info" (
                                       "id" int8 NOT NULL,
                                       "real_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                       "email" varchar(50) COLLATE "pg_catalog"."default",
                                       "mobile" varchar(15) COLLATE "pg_catalog"."default",
                                       "type" varchar(15) COLLATE "pg_catalog"."default" NOT NULL,
                                       "team_id" int8 NOT NULL,
                                       "job_name" varchar(20) COLLATE "pg_catalog"."default",
                                       "is_team_leader" int2,
                                       "section_id" int8 NOT NULL,
                                       "res_id" int8 NOT NULL,
                                       "created_by" int8 NOT NULL,
                                       "created_at" timestamp(6) NOT NULL,
                                       "updated_by" int8 NOT NULL,
                                       "updated_at" timestamp(6) NOT NULL,
                                       "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."staff_info"."id" IS '用户id';
COMMENT ON COLUMN "public"."staff_info"."real_name" IS '真实姓名';
COMMENT ON COLUMN "public"."staff_info"."email" IS '邮箱';
COMMENT ON COLUMN "public"."staff_info"."mobile" IS '手机号';
COMMENT ON COLUMN "public"."staff_info"."type" IS '特种-special， 普通-common';
COMMENT ON COLUMN "public"."staff_info"."team_id" IS '部门班组id';
COMMENT ON COLUMN "public"."staff_info"."job_name" IS '工种数据字典key';
COMMENT ON COLUMN "public"."staff_info"."is_team_leader" IS '是否是班组长：0-不是， 1-是';
COMMENT ON COLUMN "public"."staff_info"."section_id" IS '标段id';
COMMENT ON COLUMN "public"."staff_info"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."staff_info"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."staff_info"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."staff_info"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."staff_info"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."staff_info"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."staff_info" IS '作业人员信息表';

-- ----------------------------
-- Table structure for sys_linkage_setting
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_linkage_setting";
CREATE TABLE "public"."sys_linkage_setting" (
                                                "id" int8 NOT NULL,
                                                "linkage_module" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                "linkage_switch" int2 NOT NULL,
                                                "res_id" int8 NOT NULL,
                                                "created_by" int8 NOT NULL,
                                                "created_at" timestamp(6) NOT NULL,
                                                "updated_by" int8 NOT NULL,
                                                "updated_at" timestamp(6) NOT NULL,
                                                "business_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_linkage_setting"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_linkage_setting"."linkage_module" IS '关联模块';
COMMENT ON COLUMN "public"."sys_linkage_setting"."linkage_switch" IS '关联开关: 0-独立，1-关联';
COMMENT ON COLUMN "public"."sys_linkage_setting"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."sys_linkage_setting"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."sys_linkage_setting"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."sys_linkage_setting"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."sys_linkage_setting"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."sys_linkage_setting"."business_id" IS '关联主体id：例如角色菜单，主体是角色；人员项目标段，主体是人员；';
COMMENT ON TABLE "public"."sys_linkage_setting" IS '用户层级联动设置表';

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_menu";
CREATE TABLE "public"."sys_menu" (
                                     "id" int8 NOT NULL,
                                     "name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                     "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                     "parent_id" int8,
                                     "order_num" int2 NOT NULL,
                                     "url" varchar(255) COLLATE "pg_catalog"."default",
                                     "component" varchar(255) COLLATE "pg_catalog"."default",
                                     "status" int2 NOT NULL DEFAULT 0,
                                     "icon" varchar(30) COLLATE "pg_catalog"."default",
                                     "path" int8[],
                                     "memo" varchar(50) COLLATE "pg_catalog"."default",
                                     "path_id" varchar(8) COLLATE "pg_catalog"."default",
                                     "is_link" int2 NOT NULL DEFAULT 0,
                                     "sys_module_id" int8 NOT NULL,
                                     "redirect" varchar(255) COLLATE "pg_catalog"."default",
                                     "sys_module_code" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_menu"."id" IS '菜单id';
COMMENT ON COLUMN "public"."sys_menu"."name" IS '菜单名称';
COMMENT ON COLUMN "public"."sys_menu"."type" IS '菜单类型：目录-dir、菜单-menu、按钮-btn';
COMMENT ON COLUMN "public"."sys_menu"."parent_id" IS '菜单父id';
COMMENT ON COLUMN "public"."sys_menu"."order_num" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_menu"."url" IS '路由地址';
COMMENT ON COLUMN "public"."sys_menu"."component" IS '组件路径';
COMMENT ON COLUMN "public"."sys_menu"."status" IS '菜单状态，0-停用，1-正常';
COMMENT ON COLUMN "public"."sys_menu"."icon" IS '图标';
COMMENT ON COLUMN "public"."sys_menu"."path" IS '菜单路径';
COMMENT ON COLUMN "public"."sys_menu"."memo" IS '备注';
COMMENT ON COLUMN "public"."sys_menu"."path_id" IS '路径id';
COMMENT ON COLUMN "public"."sys_menu"."is_link" IS '是否为外链，0-否，1-是';
COMMENT ON COLUMN "public"."sys_menu"."sys_module_id" IS '所属系统模块id';
COMMENT ON COLUMN "public"."sys_menu"."redirect" IS '一级菜单跳转路径';
COMMENT ON COLUMN "public"."sys_menu"."sys_module_code" IS '系统模块编码';
COMMENT ON TABLE "public"."sys_menu" IS '菜单表';

-- ----------------------------
-- Table structure for sys_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_module";
CREATE TABLE "public"."sys_module" (
                                       "id" int8 NOT NULL,
                                       "name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                       "order_num" int4 NOT NULL,
                                       "is_link" int2 NOT NULL,
                                       "url" varchar(255) COLLATE "pg_catalog"."default",
                                       "memo" varchar(50) COLLATE "pg_catalog"."default",
                                       "type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                       "code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_module"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_module"."name" IS '系统模块名称';
COMMENT ON COLUMN "public"."sys_module"."order_num" IS '系统排序';
COMMENT ON COLUMN "public"."sys_module"."is_link" IS '是否为外链，0-否，1-是';
COMMENT ON COLUMN "public"."sys_module"."url" IS '路由地址';
COMMENT ON COLUMN "public"."sys_module"."memo" IS '系统备注说明';
COMMENT ON COLUMN "public"."sys_module"."type" IS '系统类型，0-web，1-app';
COMMENT ON COLUMN "public"."sys_module"."code" IS '系统编码';

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_permission";
CREATE TABLE "public"."sys_permission" (
                                           "id" int8 NOT NULL,
                                           "permission_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                           "menu_id" int4 NOT NULL,
                                           "lowest_version" int4 NOT NULL,
                                           "height_version" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_permission"."id" IS '权限码id';
COMMENT ON COLUMN "public"."sys_permission"."permission_name" IS '权限名称';
COMMENT ON COLUMN "public"."sys_permission"."menu_id" IS '菜单id';
COMMENT ON COLUMN "public"."sys_permission"."lowest_version" IS '最低支持版本号';
COMMENT ON COLUMN "public"."sys_permission"."height_version" IS '最高支持版本号';
COMMENT ON TABLE "public"."sys_permission" IS '权限码表';

-- ----------------------------
-- Table structure for sys_permission_path
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_permission_path";
CREATE TABLE "public"."sys_permission_path" (
                                                "id" int8 NOT NULL,
                                                "permission_id" int8 NOT NULL,
                                                "permission_path" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                "lowest_version" int4 NOT NULL,
                                                "height_version" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_permission_path"."id" IS '权限码路径关联id';
COMMENT ON COLUMN "public"."sys_permission_path"."permission_id" IS '权限码id';
COMMENT ON COLUMN "public"."sys_permission_path"."permission_path" IS '资源路径';
COMMENT ON COLUMN "public"."sys_permission_path"."lowest_version" IS '最低支持版本号';
COMMENT ON COLUMN "public"."sys_permission_path"."height_version" IS '最高支持版本号';
COMMENT ON TABLE "public"."sys_permission_path" IS '权限码路径关联表';

-- ----------------------------
-- Table structure for tenant_module_auth
-- ----------------------------
DROP TABLE IF EXISTS "public"."tenant_module_auth";
CREATE TABLE "public"."tenant_module_auth" (
                                               "id" int8 NOT NULL,
                                               "sys_module_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                               "tenant_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL,
                                               "created_at" timestamp(6) NOT NULL,
                                               "updated_by" int8 NOT NULL,
                                               "updated_at" timestamp(6) NOT NULL,
                                               "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_module_auth"."id" IS 'id';
COMMENT ON COLUMN "public"."tenant_module_auth"."sys_module_code" IS '系统模块编码';
COMMENT ON COLUMN "public"."tenant_module_auth"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_module_auth"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_module_auth"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_module_auth"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_module_auth"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."tenant_module_auth"."is_deleted" IS '软删除标志位，0：未删除，1：删除';

-- ----------------------------
-- Table structure for tenant_quota_assignment
-- ----------------------------
DROP TABLE IF EXISTS "public"."tenant_quota_assignment";
CREATE TABLE "public"."tenant_quota_assignment" (
                                                    "id" int8 NOT NULL,
                                                    "user_id" int8 NOT NULL,
                                                    "res_id" int8 NOT NULL,
                                                    "quota_id" int8 NOT NULL,
                                                    "created_by" int8 NOT NULL,
                                                    "created_at" timestamp(6) NOT NULL,
                                                    "updated_by" int8 NOT NULL,
                                                    "updated_at" timestamp(6) NOT NULL,
                                                    "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_quota_assignment"."id" IS '主键id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."quota_id" IS '配额id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."tenant_quota_assignment"."is_deleted" IS '软删除标志位，0：未删除，1：删除';

-- ----------------------------
-- Table structure for tenant_quota_manage
-- ----------------------------
DROP TABLE IF EXISTS "public"."tenant_quota_manage";
CREATE TABLE "public"."tenant_quota_manage" (
                                                "id" int8 NOT NULL,
                                                "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                                "tenant_id" int8 NOT NULL,
                                                "start_date" timestamp(6) NOT NULL,
                                                "end_date" timestamp(6) NOT NULL,
                                                "quantity" int4 NOT NULL,
                                                "created_by" int8 NOT NULL,
                                                "created_at" timestamp(6) NOT NULL,
                                                "updated_by" int8 NOT NULL,
                                                "updated_at" timestamp(6) NOT NULL,
                                                "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_quota_manage"."id" IS 'id';
COMMENT ON COLUMN "public"."tenant_quota_manage"."name" IS '配额名称';
COMMENT ON COLUMN "public"."tenant_quota_manage"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_quota_manage"."start_date" IS '起始时间';
COMMENT ON COLUMN "public"."tenant_quota_manage"."end_date" IS '到期时间';
COMMENT ON COLUMN "public"."tenant_quota_manage"."quantity" IS '总可分配数量';
COMMENT ON COLUMN "public"."tenant_quota_manage"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_quota_manage"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_quota_manage"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_quota_manage"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."tenant_quota_manage"."is_deleted" IS '软删除标志位，0：未删除，1：删除';

-- ----------------------------
-- Table structure for tenant_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."tenant_task";
CREATE TABLE "public"."tenant_task" (
                                        "id" int8 NOT NULL,
                                        "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                        "memo" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                        "status" int2 NOT NULL,
                                        "cron_expression" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                        "notice_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
                                        "trigger_type" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
                                        "advance_reminder_day" int4 NOT NULL,
                                        "tenant_id" int8 NOT NULL,
                                        "created_by" int8 NOT NULL,
                                        "created_at" timestamp(6) NOT NULL,
                                        "updated_by" int8 NOT NULL,
                                        "updated_at" timestamp(6) NOT NULL,
                                        "is_deleted" int2 NOT NULL,
                                        "notice_user_ids" int8[] NOT NULL,
                                        "event_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
                                        "trigger_start_time" varchar(20) COLLATE "pg_catalog"."default",
                                        "trigger_end_time" varchar(20) COLLATE "pg_catalog"."default",
                                        "trigger_times" varchar[] COLLATE "pg_catalog"."default" NOT NULL,
                                        "trigger_cycle" varchar(5)[] COLLATE "pg_catalog"."default" NOT NULL,
                                        "delay_type" varchar(12) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_task"."id" IS '主键id';
COMMENT ON COLUMN "public"."tenant_task"."name" IS '任务名称';
COMMENT ON COLUMN "public"."tenant_task"."memo" IS '任务备注';
COMMENT ON COLUMN "public"."tenant_task"."status" IS '启用状态: 0-关闭, 1-启用';
COMMENT ON COLUMN "public"."tenant_task"."cron_expression" IS 'cron表达式';
COMMENT ON COLUMN "public"."tenant_task"."notice_type" IS '通知方式：APP，SMS';
COMMENT ON COLUMN "public"."tenant_task"."trigger_type" IS '触发类型: interval-间隔、repetition-重复';
COMMENT ON COLUMN "public"."tenant_task"."advance_reminder_day" IS '提前提醒天数';
COMMENT ON COLUMN "public"."tenant_task"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_task"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_task"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_task"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_task"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."tenant_task"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON COLUMN "public"."tenant_task"."notice_user_ids" IS '通知人员';
COMMENT ON COLUMN "public"."tenant_task"."event_type" IS '事件类型，NOTICE-通知';
COMMENT ON COLUMN "public"."tenant_task"."trigger_start_time" IS '触发时刻(开始)';
COMMENT ON COLUMN "public"."tenant_task"."trigger_end_time" IS '触发时刻(结束)';
COMMENT ON COLUMN "public"."tenant_task"."trigger_times" IS '触发时刻(固定时间点)';
COMMENT ON COLUMN "public"."tenant_task"."trigger_cycle" IS '触发周期(Mon-周一、Tue-周二、Wed-周三、Thu-周四、Fri-周五、Sat-周六、Sun-周日)';
COMMENT ON COLUMN "public"."tenant_task"."delay_type" IS '提醒方式：IN_ADVANCE-提前提醒，BE_OVERDUE-逾期提醒，THE_DAY-当天提醒';

-- ----------------------------
-- Table structure for work_area
-- ----------------------------
DROP TABLE IF EXISTS "public"."work_area";
CREATE TABLE "public"."work_area" (
                                      "id" int8 NOT NULL,
                                      "section_id" int8 NOT NULL,
                                      "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                      "res_id" int8 NOT NULL,
                                      "created_by" int8 NOT NULL,
                                      "created_at" timestamp(6) NOT NULL,
                                      "updated_by" int8 NOT NULL,
                                      "updated_at" timestamp(6) NOT NULL,
                                      "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."work_area"."id" IS '工作区域表id';
COMMENT ON COLUMN "public"."work_area"."section_id" IS '标段id';
COMMENT ON COLUMN "public"."work_area"."name" IS '区域名称';
COMMENT ON COLUMN "public"."work_area"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."work_area"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."work_area"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_area"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."work_area"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."work_area"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."work_area" IS '工作区域表';

-- ----------------------------
-- Table structure for work_team
-- ----------------------------
DROP TABLE IF EXISTS "public"."work_team";
CREATE TABLE "public"."work_team" (
                                      "id" int8 NOT NULL,
                                      "section_id" int8 NOT NULL,
                                      "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                      "res_id" int8 NOT NULL,
                                      "created_by" int8 NOT NULL,
                                      "created_at" timestamp(6) NOT NULL,
                                      "updated_by" int8 NOT NULL,
                                      "updated_at" timestamp(6) NOT NULL,
                                      "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."work_team"."id" IS '作业班组表id';
COMMENT ON COLUMN "public"."work_team"."section_id" IS '标段id';
COMMENT ON COLUMN "public"."work_team"."name" IS '班组名称';
COMMENT ON COLUMN "public"."work_team"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."work_team"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."work_team"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."work_team"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."work_team"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."work_team"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."work_team" IS '作业班组表';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."city_id_seq"
    OWNED BY "public"."city"."id";
SELECT setval('"public"."city_id_seq"', 340, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."county_id_seq"
    OWNED BY "public"."county"."id";
SELECT setval('"public"."county_id_seq"', 2815, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."province_id_seq"
    OWNED BY "public"."province"."id";
SELECT setval('"public"."province_id_seq"', 34, true);

-- ----------------------------
-- Indexes structure for table city
-- ----------------------------
CREATE INDEX "idx_city_province_id" ON "public"."city" USING btree (
                                                                    "code" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table city
-- ----------------------------
ALTER TABLE "public"."city" ADD CONSTRAINT "city_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table common_key_value
-- ----------------------------
CREATE INDEX "common_key_value_k_idx" ON "public"."common_key_value" USING btree (
                                                                                  "k" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "common_key_value_res_id_idx" ON "public"."common_key_value" USING btree (
                                                                                       "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table common_key_value
-- ----------------------------
ALTER TABLE "public"."common_key_value" ADD CONSTRAINT "common_key_value_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table county
-- ----------------------------
CREATE INDEX "idx_county_city_id" ON "public"."county" USING btree (
                                                                    "code" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table county
-- ----------------------------
ALTER TABLE "public"."county" ADD CONSTRAINT "county_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table data_dictionary
-- ----------------------------
CREATE INDEX "data_dictionary_res_id_idx" ON "public"."data_dictionary" USING btree (
                                                                                     "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table data_dictionary
-- ----------------------------
ALTER TABLE "public"."data_dictionary" ADD CONSTRAINT "data_dictionary_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table data_dictionary_tree_node
-- ----------------------------
CREATE INDEX "data_dictionary_tree_node_res_id_tree_type_idx" ON "public"."data_dictionary_tree_node" USING btree (
                                                                                                                   "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                                                                                                                   "tree_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table data_dictionary_tree_node
-- ----------------------------
ALTER TABLE "public"."data_dictionary_tree_node" ADD CONSTRAINT "data_dictionary_tree_node_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table ent_project
-- ----------------------------
CREATE INDEX "ent_project_name_idx" ON "public"."ent_project" USING btree (
                                                                           "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "ent_project_res_id_idx" ON "public"."ent_project" USING btree (
                                                                             "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ent_project
-- ----------------------------
ALTER TABLE "public"."ent_project" ADD CONSTRAINT "ent_project_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table ent_resource
-- ----------------------------
CREATE INDEX "ent_resource_parent_id_idx" ON "public"."ent_resource" USING btree (
                                                                                  "parent_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "ent_resource_path_idx" ON "public"."ent_resource" USING btree (
                                                                             "path" "pg_catalog"."array_ops" ASC NULLS LAST
    );
CREATE INDEX "ent_resource_tenant_id_idx" ON "public"."ent_resource" USING btree (
                                                                                  "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Indexes structure for table ent_supplier
-- ----------------------------
CREATE INDEX "ent_supplier_name_idx" ON "public"."ent_supplier" USING btree (
                                                                             "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "ent_supplier_res_id_idx" ON "public"."ent_supplier" USING btree (
                                                                               "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ent_supplier
-- ----------------------------
ALTER TABLE "public"."ent_supplier" ADD CONSTRAINT "ent_supplier_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table ent_supplier_user
-- ----------------------------
CREATE INDEX "idx_esu_supplier_id" ON "public"."ent_supplier_user" USING btree (
                                                                                "supplier_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_esu_user_id" ON "public"."ent_supplier_user" USING btree (
                                                                            "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ent_supplier_user
-- ----------------------------
ALTER TABLE "public"."ent_supplier_user" ADD CONSTRAINT "ent_supplier_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table person_detail
-- ----------------------------
CREATE INDEX "idx_pd_id_card" ON "public"."person_detail" USING btree (
                                                                       "id_card" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_pd_person_id" ON "public"."person_detail" USING btree (
                                                                         "person_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table person_detail
-- ----------------------------
ALTER TABLE "public"."person_detail" ADD CONSTRAINT "person_detail_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table person_work_info
-- ----------------------------
CREATE INDEX "idx_pwi_person_id" ON "public"."person_work_info" USING btree (
                                                                             "person_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_pwi_res_id" ON "public"."person_work_info" USING btree (
                                                                          "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table person_work_info
-- ----------------------------
ALTER TABLE "public"."person_work_info" ADD CONSTRAINT "person_work_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table process_type
-- ----------------------------
CREATE INDEX "process_type_res_id_idx" ON "public"."process_type" USING btree (
                                                                               "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table process_type
-- ----------------------------
ALTER TABLE "public"."process_type" ADD CONSTRAINT "process_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table project_participant
-- ----------------------------
CREATE INDEX "idx_ppart_puid" ON "public"."project_participant" USING btree (
                                                                             "participant_unit_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_ppart_res_id" ON "public"."project_participant" USING btree (
                                                                               "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_ppart_sid" ON "public"."project_participant" USING btree (
                                                                            "section_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_participant
-- ----------------------------
ALTER TABLE "public"."project_participant" ADD CONSTRAINT "project_participant_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table project_section
-- ----------------------------
CREATE INDEX "project_section_name_idx" ON "public"."project_section" USING btree (
                                                                                   "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "project_section_res_id_idx" ON "public"."project_section" USING btree (
                                                                                     "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_section
-- ----------------------------
ALTER TABLE "public"."project_section" ADD CONSTRAINT "project_section_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table project_user_org
-- ----------------------------
CREATE INDEX "idx_puo_user_id" ON "public"."project_user_org" USING btree (
                                                                           "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "project_user_org_org_id_idx" ON "public"."project_user_org" USING btree (
                                                                                       "org_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE UNIQUE INDEX "project_user_org_user_id_res_id_deleted_at_idx" ON "public"."project_user_org" USING btree (
                                                                                                                 "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                                                                                                                 "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                                                                                                                 "deleted_at" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_user_org
-- ----------------------------
ALTER TABLE "public"."project_user_org" ADD CONSTRAINT "project_user_org_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table project_user_section
-- ----------------------------
CREATE INDEX "idx_pus_res_id" ON "public"."project_user_section" USING btree (
                                                                              "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_pus_section_id" ON "public"."project_user_section" USING btree (
                                                                                  "section_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "project_user_section_user_id_idx" ON "public"."project_user_section" USING btree (
                                                                                                "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_user_section
-- ----------------------------
ALTER TABLE "public"."project_user_section" ADD CONSTRAINT "project_user_section_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table province
-- ----------------------------
CREATE INDEX "idx_province_code" ON "public"."province" USING btree (
                                                                     "code" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table province
-- ----------------------------
ALTER TABLE "public"."province" ADD CONSTRAINT "province_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_attachment
-- ----------------------------
CREATE INDEX "idx_ra_relation_id" ON "public"."resource_attachment" USING btree (
                                                                                 "relation_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_attachment
-- ----------------------------
ALTER TABLE "public"."resource_attachment" ADD CONSTRAINT "resource_attachment_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_org
-- ----------------------------
CREATE INDEX "resource_org_name_idx" ON "public"."resource_org" USING btree (
                                                                             "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_org_parent_id_idx" ON "public"."resource_org" USING btree (
                                                                                  "parent_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_org_path_idx" ON "public"."resource_org" USING btree (
                                                                             "path" "pg_catalog"."array_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_org
-- ----------------------------
ALTER TABLE "public"."resource_org" ADD CONSTRAINT "resource_org_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table resource_package_auth
-- ----------------------------
ALTER TABLE "public"."resource_package_auth" ADD CONSTRAINT "resource_package_auth_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_post
-- ----------------------------
CREATE INDEX "resource_post_post_name_idx" ON "public"."resource_post" USING btree (
                                                                                    "post_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_post_res_id_idx" ON "public"."resource_post" USING btree (
                                                                                 "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_post
-- ----------------------------
ALTER TABLE "public"."resource_post" ADD CONSTRAINT "resource_post_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table resource_post_role
-- ----------------------------
ALTER TABLE "public"."resource_post_role" ADD CONSTRAINT "resource_post_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_role
-- ----------------------------
CREATE INDEX "resource_role_res_id_idx" ON "public"."resource_role" USING btree (
                                                                                 "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_role_role_name_idx" ON "public"."resource_role" USING btree (
                                                                                    "role_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_role
-- ----------------------------
ALTER TABLE "public"."resource_role" ADD CONSTRAINT "resource_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_role_menu
-- ----------------------------
CREATE INDEX "idx_rrm_role_id" ON "public"."resource_role_menu" USING btree (
                                                                             "role_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_role_menu_menu_id_idx" ON "public"."resource_role_menu" USING btree (
                                                                                            "menu_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_role_menu
-- ----------------------------
ALTER TABLE "public"."resource_role_menu" ADD CONSTRAINT "resource_role_menu_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_role_permission
-- ----------------------------
CREATE INDEX "idx_rop_role_id" ON "public"."resource_role_permission" USING btree (
                                                                                   "role_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_role_permission_res_id_idx" ON "public"."resource_role_permission" USING btree (
                                                                                                       "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_role_permission
-- ----------------------------
ALTER TABLE "public"."resource_role_permission" ADD CONSTRAINT "resource_role_permission_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_supplier_ref
-- ----------------------------
CREATE INDEX "resource_supplier_ref_res_id_idx" ON "public"."resource_supplier_ref" USING btree (
                                                                                                 "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_supplier_ref_supplier_id_idx" ON "public"."resource_supplier_ref" USING btree (
                                                                                                      "supplier_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Indexes structure for table resource_user
-- ----------------------------
CREATE INDEX "idx_ru_real_name" ON "public"."resource_user" USING btree (
                                                                         "real_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_ru_username" ON "public"."resource_user" USING btree (
                                                                        "username" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_user_res_id_idx" ON "public"."resource_user" USING btree (
                                                                                 "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_user
-- ----------------------------
ALTER TABLE "public"."resource_user" ADD CONSTRAINT "resource_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_user_credentials
-- ----------------------------
CREATE INDEX "idx_ruc_user_id" ON "public"."resource_user_credentials" USING btree (
                                                                                    "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_user_credentials
-- ----------------------------
ALTER TABLE "public"."resource_user_credentials" ADD CONSTRAINT "resource_user_credentials_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_user_org
-- ----------------------------
CREATE INDEX "idx_ruo_org_id" ON "public"."resource_user_org" USING btree (
                                                                           "org_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_ruo_user_id" ON "public"."resource_user_org" USING btree (
                                                                            "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_user_org
-- ----------------------------
ALTER TABLE "public"."resource_user_org" ADD CONSTRAINT "resource_user_org_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table resource_user_role
-- ----------------------------
CREATE INDEX "idx_rur_user_id" ON "public"."resource_user_role" USING btree (
                                                                             "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_user_role_res_id_idx" ON "public"."resource_user_role" USING btree (
                                                                                           "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "resource_user_role_role_ids_idx" ON "public"."resource_user_role" USING btree (
                                                                                             "role_ids" "pg_catalog"."array_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table resource_user_role
-- ----------------------------
ALTER TABLE "public"."resource_user_role" ADD CONSTRAINT "resource_user_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table staff_info
-- ----------------------------
CREATE INDEX "idx_si_real_name" ON "public"."staff_info" USING btree (
                                                                      "real_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_si_res_id" ON "public"."staff_info" USING btree (
                                                                   "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_si_section_id" ON "public"."staff_info" USING btree (
                                                                       "section_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_si_team_id" ON "public"."staff_info" USING btree (
                                                                    "team_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table staff_info
-- ----------------------------
ALTER TABLE "public"."staff_info" ADD CONSTRAINT "staff_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_linkage_setting
-- ----------------------------
CREATE INDEX "sys_linkage_setting_business_id_idx" ON "public"."sys_linkage_setting" USING btree (
                                                                                                  "business_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "sys_linkage_setting_res_id_idx" ON "public"."sys_linkage_setting" USING btree (
                                                                                             "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table sys_linkage_setting
-- ----------------------------
ALTER TABLE "public"."sys_linkage_setting" ADD CONSTRAINT "sys_linkage_setting_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_menu
-- ----------------------------
CREATE INDEX "idx_sys_menu_path" ON "public"."sys_menu" USING btree (
                                                                     "path" "pg_catalog"."array_ops" ASC NULLS LAST
    );
CREATE INDEX "sys_module_code" ON "public"."sys_menu" USING btree (
                                                                   "sys_module_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table sys_menu
-- ----------------------------
ALTER TABLE "public"."sys_menu" ADD CONSTRAINT "sys_menu_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_module
-- ----------------------------
CREATE INDEX "sys_module_code_idx" ON "public"."sys_module" USING btree (
                                                                         "code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table sys_module
-- ----------------------------
ALTER TABLE "public"."sys_module" ADD CONSTRAINT "sys_module_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_permission
-- ----------------------------
CREATE INDEX "sys_permission_menu_id_idx" ON "public"."sys_permission" USING btree (
                                                                                    "menu_id" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table sys_permission
-- ----------------------------
ALTER TABLE "public"."sys_permission" ADD CONSTRAINT "sys_permission_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_permission_path
-- ----------------------------
CREATE INDEX "sys_permission_path_permission_id_idx" ON "public"."sys_permission_path" USING btree (
                                                                                                    "permission_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table sys_permission_path
-- ----------------------------
ALTER TABLE "public"."sys_permission_path" ADD CONSTRAINT "sys_permission_path_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tenant_module_auth
-- ----------------------------
CREATE INDEX "tenant_module_auth_tenant_id_idx" ON "public"."tenant_module_auth" USING btree (
                                                                                              "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_module_auth
-- ----------------------------
ALTER TABLE "public"."tenant_module_auth" ADD CONSTRAINT "tenant_module_auth_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tenant_quota_assignment
-- ----------------------------
CREATE INDEX "tenant_quota_assignment_quota_id_idx" ON "public"."tenant_quota_assignment" USING btree (
                                                                                                       "quota_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "tenant_quota_assignment_res_id_idx" ON "public"."tenant_quota_assignment" USING btree (
                                                                                                     "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );
CREATE INDEX "tenant_quota_assignment_user_id_idx" ON "public"."tenant_quota_assignment" USING btree (
                                                                                                      "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_quota_assignment
-- ----------------------------
ALTER TABLE "public"."tenant_quota_assignment" ADD CONSTRAINT "tenant_quota_assignment_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tenant_quota_manage
-- ----------------------------
CREATE INDEX "tenant_quota_manage_tenant_id_idx" ON "public"."tenant_quota_manage" USING btree (
                                                                                                "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_quota_manage
-- ----------------------------
ALTER TABLE "public"."tenant_quota_manage" ADD CONSTRAINT "tenant_quota_manage_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table tenant_task
-- ----------------------------
CREATE INDEX "tenant_task_tenant_id_idx" ON "public"."tenant_task" USING btree (
                                                                                "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_task
-- ----------------------------
ALTER TABLE "public"."tenant_task" ADD CONSTRAINT "tenant_task_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table work_area
-- ----------------------------
CREATE INDEX "idx_wa_res_id" ON "public"."work_area" USING btree (
                                                                  "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table work_area
-- ----------------------------
ALTER TABLE "public"."work_area" ADD CONSTRAINT "work_area_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table work_team
-- ----------------------------
CREATE INDEX "idx_wt_res_id" ON "public"."work_team" USING btree (
                                                                  "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table work_team
-- ----------------------------
ALTER TABLE "public"."work_team" ADD CONSTRAINT "work_team_pkey" PRIMARY KEY ("id");
