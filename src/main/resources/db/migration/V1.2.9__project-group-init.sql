CREATE TABLE "public"."project_group" (
                                          "id" int8 NOT NULL,
                                          "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
                                          "res_id" int8 NOT NULL,
                                          "created_by" int8 NOT NULL DEFAULT 0,
                                          "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                          "updated_by" int8 NOT NULL DEFAULT 0,
                                          "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                          "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."project_group"."id" IS '节点id';
COMMENT ON COLUMN "public"."project_group"."name" IS '组织名称';
COMMENT ON COLUMN "public"."project_group"."res_id" IS '资源id';
COMMENT ON COLUMN "public"."project_group"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_group"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_group"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_group"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_group"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."project_group" IS '资源组织结构表';

-- ----------------------------
-- Indexes structure for table project_group
-- ----------------------------
CREATE INDEX "project_group_res_id_idx" ON "public"."project_group" USING btree (
    "res_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_group
-- ----------------------------
ALTER TABLE "public"."project_group" ADD CONSTRAINT "project_group_pkey" PRIMARY KEY ("id");

CREATE TABLE "public"."project_group_user" (
                                               "id" int8 NOT NULL,
                                               "user_id" int8 NOT NULL,
                                               "project_group_id" int8 NOT NULL,
                                               "created_by" int8 NOT NULL DEFAULT 0,
                                               "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                               "updated_by" int8 NOT NULL DEFAULT 0,
                                               "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                               "is_deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."project_group_user"."id" IS '节点id';
COMMENT ON COLUMN "public"."project_group_user"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."project_group_user"."project_group_id" IS '项目组id';
COMMENT ON COLUMN "public"."project_group_user"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."project_group_user"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."project_group_user"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."project_group_user"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."project_group_user"."is_deleted" IS '软删除标志位，0：未删除，1：删除';
COMMENT ON TABLE "public"."project_group_user" IS '资源组织结构表';

-- ----------------------------
-- Indexes structure for table project_group_user
-- ----------------------------
CREATE INDEX "project_group_user_project_group_id_idx" ON "public"."project_group_user" USING btree (
    "project_group_id" "pg_catalog"."int8_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table project_group_user
-- ----------------------------
ALTER TABLE "public"."project_group_user" ADD CONSTRAINT "project_group_user_pkey" PRIMARY KEY ("id");

ALTER TABLE "public"."resource_supplier_ref"
    ADD PRIMARY KEY ("id");