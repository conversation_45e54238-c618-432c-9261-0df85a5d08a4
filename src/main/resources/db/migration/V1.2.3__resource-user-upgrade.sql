ALTER TABLE "public"."resource_user"
ALTER COLUMN "username" TYPE varchar(100) COLLATE "pg_catalog"."default",
  ALTER COLUMN "real_name" TYPE varchar(50) COLLATE "pg_catalog"."default",
  ALTER COLUMN "username" DROP NOT NULL,
  ADD COLUMN "account_id" int8;

COMMENT ON COLUMN "public"."resource_user"."account_id" IS '账号id';

ALTER TABLE "public"."resource_user_role"
    ADD COLUMN "res_type" varchar(10);

COMMENT ON COLUMN "public"."resource_user_role"."res_type" IS '资源类型 ent-企业，cmp-公司，project-项目';