-- 菜单字段调整
ALTER TABLE "public"."sys_menu"
    DROP COLUMN "path_id",
    ADD COLUMN "permission_code" varchar(255);
COMMENT ON COLUMN "public"."sys_menu"."permission_code" IS '权限码';

-- 菜单顺序调整
UPDATE "public"."sys_menu" SET "order_num" = 37  WHERE "id" = 1714256290457591889;
UPDATE "public"."sys_menu" SET "order_num" = 38 WHERE "id" = 1714256290457591857;
UPDATE "public"."sys_menu" SET "order_num" = 41 WHERE "id" = 1714256290457591868;
UPDATE "public"."sys_menu" SET "order_num" = 42 WHERE "id" = 1714256290457591863;
UPDATE "public"."sys_menu" SET "order_num" = 61 WHERE "id" = 1714256290457591892;
UPDATE "public"."sys_menu" SET "order_num" = 62 WHERE "id" = 1714256290457591885;
UPDATE "public"."sys_menu" SET "order_num" = 63 WHERE "id" = 1714256290457591877;
UPDATE "public"."sys_menu" SET "order_num" = 64 WHERE "id" = 1714256290457591886;
