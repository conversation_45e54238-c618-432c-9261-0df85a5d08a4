CREATE TABLE "public"."ent_resource_schema" (
                                                "id" int8 NOT NULL,
                                                "type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                                "extra_info_schema" text COLLATE "pg_catalog"."default" NOT NULL,
                                                "created_at" timestamp(6) NOT NULL,
                                                "created_by" int8 NOT NULL,
                                                "updated_at" timestamp(6) NOT NULL,
                                                "updated_by" int8 NOT NULL,
                                                "is_deleted" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."ent_resource_schema"."id" IS '主键id';
COMMENT ON COLUMN "public"."ent_resource_schema"."type" IS '资源类型';
COMMENT ON COLUMN "public"."ent_resource_schema"."extra_info_schema" IS '项目扩展信息schema';
COMMENT ON COLUMN "public"."ent_resource_schema"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ent_resource_schema"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."ent_resource_schema"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ent_resource_schema"."updated_by" IS '更新人id';
COMMENT ON COLUMN "public"."ent_resource_schema"."is_deleted" IS '删除标记';

-- ----------------------------
-- Indexes structure for table ent_resource_schema
-- ----------------------------
CREATE INDEX "ent_resource_schema_type_idx" ON "public"."ent_resource_schema" USING btree (
    "type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ent_resource_schema
-- ----------------------------
ALTER TABLE "public"."ent_resource_schema" ADD CONSTRAINT "ent_resource_schema_pkey" PRIMARY KEY ("id");
