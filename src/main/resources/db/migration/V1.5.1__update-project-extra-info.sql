UPDATE ent_project
SET extra_info = CASE
                     WHEN jsonb_typeof(extra_info -> 'startDate') = 'null' THEN
                         extra_info - 'startDate'
                     WHEN jsonb_typeof(extra_info -> 'startDate') = 'string' THEN
                         extra_info - 'startDate'
                     WHEN extra_info -> 'startDate' = '0' THEN
                         extra_info - 'startDate'
                     ELSE
                         extra_info
    END;

UPDATE ent_project
SET extra_info = CASE
                     WHEN jsonb_typeof(extra_info -> 'endDate') = 'null' THEN
                         extra_info - 'endDate'
                     WHEN jsonb_typeof(extra_info -> 'endDate') = 'string' THEN
                         extra_info - 'endDate'
                     WHEN extra_info -> 'endDate' = '0' THEN
                         extra_info - 'endDate'
                     ELSE
                         extra_info
    END;

UPDATE ent_project
SET extra_info = CASE
                     WHEN jsonb_typeof(extra_info -> 'approvalDate') = 'null' THEN
                         extra_info - 'approvalDate'
                     WHEN jsonb_typeof(extra_info -> 'approvalDate') = 'string' THEN
                         extra_info - 'approvalDate'
                     WHEN extra_info -> 'approvalDate' = '0' THEN
                         extra_info - 'approvalDate'
                     ELSE
                         extra_info
    END;