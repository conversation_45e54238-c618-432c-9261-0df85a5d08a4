CREATE TABLE "public"."resource_user_function"
(
    "id"            int8                                       NOT NULL,
    "user_id"       int8                                       NOT NULL,
    "function_id"   int8                                       NOT NULL,
    "function_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
    "order_num"     int4                                       NOT NULL,
    "res_id"        int8                                       NOT NULL,
    "created_by"    int8                                       NOT NULL,
    "created_at"    timestamp(6)                               NOT NULL,
    "updated_by"    int8                                       NOT NULL,
    "updated_at"    timestamp(6)                               NOT NULL,
    "is_deleted"    int2                                       NOT NULL,
    PRIMARY KEY ("id")
)
;

CREATE INDEX "resource_user_function_user_id_idx" ON "public"."resource_user_function" USING btree (
                                                                                            "user_id"
                                                                                            "pg_catalog"."int8_ops" ASC
                                                                                            NULLS LAST
    );