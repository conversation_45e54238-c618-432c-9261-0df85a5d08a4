ALTER TABLE "public"."data_dictionary"
    ALTER COLUMN "node_id" DROP NOT NULL;

ALTER TABLE "public"."data_dictionary"
    ADD COLUMN "tree_key" varchar(50),
    ADD COLUMN "tree_type" varchar(50);

ALTER TABLE "public"."data_dictionary"
    ADD UNIQUE ("key", "tree_key", "tree_type", "res_id");

COMMENT ON COLUMN "public"."data_dictionary"."tree_key" IS '字典所属的树key';
COMMENT ON COLUMN "public"."data_dictionary"."tree_type" IS '字典所属的树type';

ALTER TABLE "public"."data_dictionary_tree_node"
    ADD COLUMN "version" int8 NOT NULL DEFAULT 1;

update data_dictionary dd set tree_key = ddtn.key, tree_type = ddtn.tree_type from data_dictionary_tree_node ddtn where dd.node_id = ddtn.id and ddtn.res_id = 0;