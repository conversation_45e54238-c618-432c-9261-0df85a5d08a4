CREATE TABLE "public"."tenant_setting" (
                                           "id" int8 NOT NULL,
                                           "tenant_id" int8 NOT NULL,
                                           "config_key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                           "config_val" text COLLATE "pg_catalog"."default" NOT NULL,
                                           "is_deleted" int2 NOT NULL,
                                           "created_at" timestamp(6) NOT NULL,
                                           "created_by" int8 NOT NULL,
                                           "updated_at" timestamp(6) NOT NULL,
                                           "updated_by" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_setting"."id" IS '主键';
COMMENT ON COLUMN "public"."tenant_setting"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_setting"."config_key" IS '配置key';
COMMENT ON COLUMN "public"."tenant_setting"."config_val" IS '配置val';
COMMENT ON COLUMN "public"."tenant_setting"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."tenant_setting"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_setting"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."tenant_setting"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."tenant_setting"."updated_by" IS '更新人';
COMMENT ON TABLE "public"."tenant_setting" IS '租户配置表';

-- ----------------------------
-- Indexes structure for table tenant_setting
-- ----------------------------
CREATE INDEX "tenant_setting_tenant_id_config_key_idx" ON "public"."tenant_setting" USING btree (
    "tenant_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
    "config_key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table tenant_setting
-- ----------------------------
ALTER TABLE "public"."tenant_setting" ADD CONSTRAINT "tenant_setting_pkey" PRIMARY KEY ("id");

CREATE TABLE "public"."tenant_setting_key" (
                                               "id" int8 NOT NULL,
                                               "config_key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
                                               "config_schema" text COLLATE "pg_catalog"."default" NOT NULL,
                                               "is_deleted" int2 NOT NULL,
                                               "created_at" timestamp(6) NOT NULL,
                                               "created_by" int8 NOT NULL,
                                               "updated_at" timestamp(6) NOT NULL,
                                               "updated_by" int8 NOT NULL,
                                               "config_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."tenant_setting_key"."id" IS '主键';
COMMENT ON COLUMN "public"."tenant_setting_key"."config_key" IS '配置key';
COMMENT ON COLUMN "public"."tenant_setting_key"."config_schema" IS '配置val';
COMMENT ON COLUMN "public"."tenant_setting_key"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."tenant_setting_key"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_setting_key"."created_by" IS '创建人';
COMMENT ON COLUMN "public"."tenant_setting_key"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."tenant_setting_key"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."tenant_setting_key"."config_name" IS '配置名称';
COMMENT ON TABLE "public"."tenant_setting_key" IS '租户配置key声明表';

-- ----------------------------
-- Indexes structure for table tenant_setting_key
-- ----------------------------
CREATE INDEX "tenant_setting_key_config_key_idx" ON "public"."tenant_setting_key" USING btree (
    "config_key" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Uniques structure for table tenant_setting_key
-- ----------------------------
ALTER TABLE "public"."tenant_setting_key" ADD CONSTRAINT "tenant_setting_key_config_key_key" UNIQUE ("config_key");

-- ----------------------------
-- Primary Key structure for table tenant_setting_key
-- ----------------------------
ALTER TABLE "public"."tenant_setting_key" ADD CONSTRAINT "tenant_setting_copy1_pkey" PRIMARY KEY ("id");