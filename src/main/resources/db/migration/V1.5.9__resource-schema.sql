DELETE FROM "public"."ent_resource_type" WHERE "id" = 10000006;

INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877190968594685953, 'ent', '{"type": "object", "title": "企业", "properties": {"memo": {"type": "string", "title": "备注", "maxLength": 200}, "mobile": {"type": "string", "title": "联系电话", "pattern": "^1[3-9]\\d{9}|((0\\d{2,3}-\\d{7,8})|(1[3456789]\\d{9}))$", "maxLength": 15}, "address": {"type": "string", "title": "联系地址", "maxLength": 200}, "contactPerson": {"type": "string", "title": "联系人", "maxLength": 20}}}', '2025-01-09 11:09:38.093', 0, '2025-01-09 11:10:42.834', 0, 0);
INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877190999552843778, 'cmp', '{"type": "object", "title": "公司", "properties": {"memo": {"type": "string", "title": "备注", "maxLength": 200}, "mobile": {"type": "string", "title": "联系电话", "pattern": "^1[3-9]\\d{9}|((0\\d{2,3}-\\d{7,8})|(1[3456789]\\d{9}))$", "maxLength": 15}, "address": {"type": "string", "title": "联系地址", "maxLength": 200}, "contactPerson": {"type": "string", "title": "联系人", "maxLength": 20}}}', '2025-01-09 11:09:45.482', 0, '2025-01-09 11:10:49.298', 0, 0);
INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877191291149246466, 'dept', '{"type": "object", "title": "部门", "properties": {"memo": {"type": "string", "title": "备注", "maxLength": 200}, "mobile": {"type": "string", "title": "联系电话", "pattern": "^1[3-9]\\d{9}|((0\\d{2,3}-\\d{7,8})|(1[3456789]\\d{9}))$", "maxLength": 15}, "address": {"type": "string", "title": "联系地址", "maxLength": 200}, "contactPerson": {"type": "string", "title": "联系人", "maxLength": 20}}}', '2025-01-09 11:10:55.001', 0, '2025-01-09 11:10:55.001', 0, 0);
INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877191319754399745, 'group', '{"type": "object", "title": "小组", "properties": {"memo": {"type": "string", "title": "备注", "maxLength": 200}, "mobile": {"type": "string", "title": "联系电话", "pattern": "^1[3-9]\\d{9}|((0\\d{2,3}-\\d{7,8})|(1[3456789]\\d{9}))$", "maxLength": 15}, "address": {"type": "string", "title": "联系地址", "maxLength": 200}, "contactPerson": {"type": "string", "title": "联系人", "maxLength": 20}}}', '2025-01-09 11:11:01.819', 0, '2025-01-09 11:11:01.819', 0, 0);
INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877193942247170049, 'project', '{"type":"object","title":"项目","required":["shortName","code","type","status"],"properties":{"code":{"type":"string","title":"项目编码","maxLength":200},"type":{"type":"string","title":"项目类型","maxLength":10},"status":{"enum":["NJ","ZJ","JG","SYY","TG"],"title":"项目状态:NJ=拟建；ZJ=在建；JG=竣工，SYY=试运营，TG=停工"},"profile":{"type":"string","title":"项目概况"},"extraInfo":{"type":"object","title":"项目扩展属性"},"shortName":{"type":"string","title":"项目简称","maxLength":200},"haveSection":{"type":"boolean","title":"是否有标段"}}}', '2025-01-09 11:21:27.069', 0, '2025-01-09 11:45:44.21', 0, 0);
INSERT INTO "public"."ent_resource_schema" ("id", "type", "extra_info_schema", "created_at", "created_by", "updated_at", "updated_by", "is_deleted") VALUES (1877226308680310786, 'section', '{"type": "object", "title": "标段", "required": ["shortName", "code", "type"], "properties": {"code": {"type": "string", "title": "标段号", "maxLength": 200}, "rate": {"type": "number", "title": "费率", "maximum": 9999, "minimum": 0}, "type": {"enum": ["SG", "JL", "JC", "JLSYS", "ZXSYS", "GH", "ZZ", "LQ", "MONITOR", "YH", "LC"], "title": "类别 SG=施工；JL=监理；JC=检测；JLSYS=监理试验室；ZXSYS=中心试验室；GH=供货；ZZ=制造；LQ=沥青；MONITOR=监测；YH=养护；LC=梁场"}, "endDate": {"type": "number", "title": "结束日期(毫秒时间戳)", "minimum": 0}, "extraInfo": {"type": "object", "title": "项目扩展属性"}, "shortName": {"type": "string", "title": "标段简称", "maxLength": 200}, "startDate": {"type": "number", "title": "开始日期(毫秒时间戳)", "minimum": 0}, "contractCode": {"type": "string", "title": "合同编号", "maxLength": 200}, "baseBidBudget": {"type": "number", "title": "标底预算", "maximum": 999999999999999999, "minimum": 0}, "contractAmount": {"type": "number", "title": "合同金额(单位:元)", "maximum": 999999999999999999, "minimum": 0}}}', '2025-01-09 13:30:03.833', 0, '2025-01-09 13:30:03.833', 0, 0);
