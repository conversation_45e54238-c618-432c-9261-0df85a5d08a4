-- ----------------------------
-- Table structure for sys_menu_v3
-- ----------------------------
CREATE TABLE "public"."sys_menu_v3" (
                                        "id" int8 NOT NULL,
                                        "platform" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                        "tags" varchar(32)[] COLLATE "pg_catalog"."default" NOT NULL,
                                        "code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                        "path" varchar(64)[] COLLATE "pg_catalog"."default" NOT NULL,
                                        "order_num" int2 NOT NULL DEFAULT 0,
                                        "status" int2 NOT NULL DEFAULT 0,
                                        "url" varchar(512) COLLATE "pg_catalog"."default",
                                        "features" varchar(64)[] COLLATE "pg_catalog"."default",
                                        "icon_name" varchar(128) COLLATE "pg_catalog"."default",
                                        "remarks" text COLLATE "pg_catalog"."default",
                                        "redirect" varchar(255) COLLATE "pg_catalog"."default",
                                        "sys_module_code" varchar(255) COLLATE "pg_catalog"."default",
                                        "created_by" int8 NOT NULL DEFAULT 0,
                                        "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                        "updated_by" int8 NOT NULL DEFAULT 0,
                                        "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP
);

COMMENT ON COLUMN "public"."sys_menu_v3"."id" IS '菜单id';
COMMENT ON COLUMN "public"."sys_menu_v3"."platform" IS '平台名称，数管平台-SG， 运维平台-YW';
COMMENT ON COLUMN "public"."sys_menu_v3"."tags" IS '菜单标签，扩展字段，标识web/app菜单，数组类';
COMMENT ON COLUMN "public"."sys_menu_v3"."code" IS '菜单编码，同一平台下，菜单编码唯一，不可修改';
COMMENT ON COLUMN "public"."sys_menu_v3"."path" IS '菜单名称全路径，数组类型';
COMMENT ON COLUMN "public"."sys_menu_v3"."order_num" IS '同一父节下排序值，越大排序越靠后';
COMMENT ON COLUMN "public"."sys_menu_v3"."status" IS '菜单状态，0-停用，1-正常';
COMMENT ON COLUMN "public"."sys_menu_v3"."url" IS '路由地址/外链url';
COMMENT ON COLUMN "public"."sys_menu_v3"."features" IS '特性扩展字段，ExtLink-外链，数组';
COMMENT ON COLUMN "public"."sys_menu_v3"."icon_name" IS '图标名称';
COMMENT ON COLUMN "public"."sys_menu_v3"."remarks" IS '备注';
COMMENT ON COLUMN "public"."sys_menu_v3"."redirect" IS '一级菜单跳转路径（兼容前端，暂时保留），可以通过url+features组合代替这个字段';
COMMENT ON COLUMN "public"."sys_menu_v3"."sys_module_code" IS '系统模块编码（兼容前端，暂时保留）';
COMMENT ON COLUMN "public"."sys_menu_v3"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."sys_menu_v3"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."sys_menu_v3"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."sys_menu_v3"."updated_at" IS '修改时间';
COMMENT ON TABLE "public"."sys_menu_v3" IS '菜单表';

-- ----------------------------
-- Table structure for sys_permission_v3
-- ----------------------------
CREATE TABLE "public"."sys_permission_v3" (
                                              "id" int8 NOT NULL,
                                              "platform" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                              "menu_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                              "path" varchar(64)[] COLLATE "pg_catalog"."default" NOT NULL,
                                              "perm_item_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                              "order_num" int2 NOT NULL DEFAULT 0,
                                              "perm_codes" varchar(128)[] COLLATE "pg_catalog"."default",
                                              "created_by" int8 NOT NULL DEFAULT 0,
                                              "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                              "updated_by" int8 NOT NULL DEFAULT 0,
                                              "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP
)
;
COMMENT ON COLUMN "public"."sys_permission_v3"."id" IS 'id';
COMMENT ON COLUMN "public"."sys_permission_v3"."platform" IS '平台名称，数管平台-SG， 运维平台-YW';
COMMENT ON COLUMN "public"."sys_permission_v3"."menu_code" IS '权限项所属菜单编码';
COMMENT ON COLUMN "public"."sys_permission_v3"."path" IS '权限项名称path，数组，如查看、新增、删除、编辑';
COMMENT ON COLUMN "public"."sys_permission_v3"."perm_item_code" IS '权限项编码，同一个平台下，编码唯一';
COMMENT ON COLUMN "public"."sys_permission_v3"."order_num" IS '权限名称path，排序值，越大排序越靠后';
COMMENT ON COLUMN "public"."sys_permission_v3"."perm_codes" IS '权限码列表，数组';
COMMENT ON COLUMN "public"."sys_permission_v3"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."sys_permission_v3"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."sys_permission_v3"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."sys_permission_v3"."updated_at" IS '修改时间';
COMMENT ON TABLE  "public"."sys_permission_v3" IS '系统权限码';

CREATE TABLE "public"."tenant_menu_diff_v3" (
                                                "id" int8 NOT NULL,
                                                "platform" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                "tags" varchar(32)[] COLLATE "pg_catalog"."default" NOT NULL,
                                                "code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                "path" varchar(64)[] COLLATE "pg_catalog"."default" NOT NULL,
                                                "order_num" int2 NOT NULL DEFAULT 0,
                                                "status" int2 NOT NULL DEFAULT 0,
                                                "url" varchar(512) COLLATE "pg_catalog"."default",
                                                "features" varchar(64)[] COLLATE "pg_catalog"."default",
                                                "icon_name" varchar(128) COLLATE "pg_catalog"."default",
                                                "remarks" text COLLATE "pg_catalog"."default",
                                                "tenant_id" int8 NOT NULL,
                                                "is_system" int2 NOT NULL,
                                                "redirect" varchar(255) COLLATE "pg_catalog"."default",
                                                "sys_module_code" varchar(255) COLLATE "pg_catalog"."default",
                                                "created_by" int8 NOT NULL DEFAULT 0,
                                                "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                                "updated_by" int8 NOT NULL DEFAULT 0,
                                                "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP
)
;

COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."id" IS '菜单id';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."platform" IS '平台名称，数管平台-SG， 运维平台-YW';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."tags" IS '菜单标签，扩展字段，标识web/app菜单，数组类型';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."code" IS '菜单编码，同一平台下，菜单编码唯一，不可修改。租户菜单会拼接租户id';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."path" IS '菜单名称全路径，数组类型';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."order_num" IS '同一父节下排序值，越大排序越靠后';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."status" IS '菜单状态，0-停用，1-正常';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."url" IS '路由地址/外链url';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."features" IS '特性扩展字段，如配置打开新Tab页签、第三方自动登录等，数组类型';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."icon_name" IS '图标名称';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."remarks" IS '备注';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."is_system" IS '是否系统菜单，0-租户菜单 1-系统菜单';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."redirect" IS '一级菜单跳转路径（兼容前端，暂时保留），可以通过url+features组合代替这个字段';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."sys_module_code" IS '系统模块编码（兼容前端，暂时保留）';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_menu_diff_v3"."updated_at" IS '修改时间';
COMMENT ON TABLE "public"."tenant_menu_diff_v3" IS '菜单表';

-- ----------------------------
-- Table structure for tenant_permission_diff_v3
-- ----------------------------
CREATE TABLE "public"."tenant_permission_diff_v3" (
                                                      "id" int8 NOT NULL,
                                                      "platform" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "menu_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "path" varchar(64)[] COLLATE "pg_catalog"."default" NOT NULL,
                                                      "perm_item_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
                                                      "order_num" int2 NOT NULL DEFAULT 0,
                                                      "perm_codes" varchar(128)[] COLLATE "pg_catalog"."default",
                                                      "tenant_id" int8 NOT NULL,
                                                      "is_system" int2 NOT NULL,
                                                      "created_by" int8 NOT NULL DEFAULT 0,
                                                      "created_at" timestamp(6) DEFAULT LOCALTIMESTAMP,
                                                      "updated_by" int8 NOT NULL DEFAULT 0,
                                                      "updated_at" timestamp(6) DEFAULT LOCALTIMESTAMP
)
;
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."id" IS 'id';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."platform" IS '平台名称，数管平台-SG， 运维平台-YW';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."menu_code" IS '权限项所属菜单编码';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."path" IS '权限项名称path，数组，如查看、新增、删除、编辑';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."perm_item_code" IS '权限项编码，同一平台下，编码唯一';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."order_num" IS '权限名称path，排序值，越大排序越靠后';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."perm_codes" IS '权限码列表，数组';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."tenant_id" IS '租户id';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."is_system" IS '是否系统菜单，0-租户菜单 1-系统菜单';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."created_by" IS '创建人id';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."updated_by" IS '修改人id';
COMMENT ON COLUMN "public"."tenant_permission_diff_v3"."updated_at" IS '修改时间';
COMMENT ON TABLE "public"."tenant_permission_diff_v3" IS '租户权限码（差异部分）';

-- ----------------------------
-- Primary Key structure for table sys_menu_v3
-- ----------------------------
ALTER TABLE "public"."sys_menu_v3" ADD CONSTRAINT "sys_menu_v3_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_permission_v3
-- ----------------------------
ALTER TABLE "public"."sys_permission_v3" ADD CONSTRAINT "sys_permission_v3_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_menu_diff_v3
-- ----------------------------
ALTER TABLE "public"."tenant_menu_diff_v3" ADD CONSTRAINT "tenant_menu_diff_v3_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table tenant_permission_diff_v3
-- ----------------------------
ALTER TABLE "public"."tenant_permission_diff_v3" ADD CONSTRAINT "tenant_permission_diff_v3_pkey" PRIMARY KEY ("id");

