-- 同步资源类型字段
update "public"."resource_user_role" rus set res_type = er.type from ent_resource er where er.id = rus.res_id;

-- 删除重复数据
update "public"."resource_user_role" set is_deleted = 1 where id in (
    SELECT id FROM (
                       SELECT id, user_id, ROW_NUMBER() OVER (PARTITION BY user_id, res_id order by updated_at desc) AS row_num
                       FROM resource_user_role where is_deleted = 0
                   ) AS t1
    WHERE row_num > 1);