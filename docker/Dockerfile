FROM harbor.luban.fit:8384/builder-d/jdk-17

# 安装字体库
RUN <<EOF bash -ex
  mkdir -p /luban
  cd /luban
  wget http://download.zhouerqin.com/public/x86_64/flyway-commandline-9.22.2-linux-x64.tar.gz
  tar -zxf flyway-commandline-9.22.2-linux-x64.tar.gz
  mv flyway-9.22.2 flyway
  rm flyway-commandline-9.22.2-linux-x64.tar.gz
EOF
ENV PATH="/luban/flyway:${PATH}"

COPY rootfs /
RUN chmod uog+x /luban/app/start.sh
CMD ["/luban/app/start.sh"]
